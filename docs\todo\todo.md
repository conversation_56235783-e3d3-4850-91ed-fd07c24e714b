# TODO List

## Backlog

- Security: Remove 'unsafe-inline' and 'unsafe-eval' from CSP headers
- Security: Implement CSRF token validation on backend
- Security: Add rate limiting for authentication endpoints
- OAuth Backend Integration - Implement backend endpoints for Google and Apple OAuth
- API Response Standardization - Work with backend to standardize API response formats
- Refactor large files: workouts.ts (674 lines)

## Completed

- [x] Critical Files Refactoring - Split useWorkout.ts and appleOAuth.ts under 225 lines (Jul 10, 2025)
- [x] Code Refactoring - Split 10 files near 225-line limit into smaller modules (Jul 10, 2025)
- [x] Security Review and Enhancement - Fixed XSS, removed localStorage tokens, added CSRF (Jul 10, 2025)
- [x] Phase 3 Integration Testing - OAuth, API errors, cache, PWA tests (Jul 10, 2025)
- [x] Fixed Authentication and CSP Issues - Added Bearer header, fixed OAuth CSP (Jul 10, 2025)
- [x] Security: Implemented httpOnly cookies for auth tokens (Jul 10, 2025)
- [x] Security: Updated CSP headers to allow OAuth providers (Jul 10, 2025
