import { apiClient } from './client'
import { logger } from '@/utils/logger'
import type {
  LoginModel,
  LoginSuccessResult,
  RegisterModel,
  BooleanModel,
} from '@/types'

/**
 * Authentication API methods
 * Handles login, registration, token refresh, and user management
 */
export const authApi = {
  /**
   * Login with email and password
   * @param credentials Login credentials
   * @returns Login success result with tokens
   */
  async login(credentials: LoginModel): Promise<LoginSuccessResult> {
    // Dr. <PERSON>scle uses form-urlencoded for token endpoint
    const formData = new URLSearchParams()
    formData.append('grant_type', 'password')
    formData.append('username', credentials.Username)
    formData.append('password', credentials.Password)

    // Debug logging to capture request info (without sensitive data)
    if (process.env.NODE_ENV === 'development') {
      logger.log('[Auth API] Login request:', {
        username: credentials.Username,
        passwordLength: credentials.Password.length,
      })
    }

    try {
      const response = await apiClient.post<LoginSuccessResult>(
        '/token',
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      )

      // Debug logging in development
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        logger.log('[Auth API] Login response:', {
          userName: response.data.userName,
          hasToken: !!response.data.access_token,
        })
      }

      return response.data
    } catch (error) {
      logger.error('Login error details:', error)
      throw error
    }
  },

  /**
   * Register a new user
   * @param userData Registration data
   * @returns Login success result with tokens
   */
  async register(userData: RegisterModel): Promise<LoginSuccessResult> {
    const response = await apiClient.post<LoginSuccessResult>(
      '/api/Account/Register',
      userData
    )
    return response.data
  },

  /**
   * Refresh access token using refresh token
   * @param refreshToken The refresh token
   * @returns New token response
   */
  async refreshToken(refreshToken: string): Promise<{
    access_token: string
    refresh_token: string
    expires_in: number
  }> {
    const response = await apiClient.post('/api/Account/Refresh', {
      refreshToken,
    })
    return response.data
  },

  // Logout is handled client-side only - no API endpoint exists

  /**
   * Request password reset email
   * @param email User's email address
   * @returns Boolean result
   */
  async forgotPassword(email: string): Promise<BooleanModel> {
    const response = await apiClient.post<BooleanModel>(
      '/Account/ForgotPassword',
      {
        Email: email,
      }
    )
    return response.data
  },

  /**
   * Verify email address with token
   * @param userId User ID
   * @param code Verification code
   * @returns Boolean result
   */
  async verifyEmail(userId: string, code: string): Promise<BooleanModel> {
    const response = await apiClient.get<BooleanModel>(
      '/Account/ConfirmEmail',
      {
        params: { userId, code },
      }
    )
    return response.data
  },

  /**
   * Resend email verification
   * @param email User's email address
   * @returns Boolean result
   */
  async resendVerificationEmail(email: string): Promise<BooleanModel> {
    const response = await apiClient.post<BooleanModel>(
      '/Account/ResendConfirmationEmail',
      {
        Email: email,
      }
    )
    return response.data
  },

  /**
   * Check if email already exists
   * @param email Email to check
   * @returns Boolean result
   */
  async isEmailAlreadyExist(email: string): Promise<BooleanModel> {
    const response = await apiClient.post<BooleanModel>(
      '/api/Account/IsEmailAlreadyExist',
      {
        Email: email,
      }
    )
    return response.data
  },
}
