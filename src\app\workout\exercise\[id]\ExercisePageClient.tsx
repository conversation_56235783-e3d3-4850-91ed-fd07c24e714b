'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { SetScreen } from '@/components/workout/SetScreen'
import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'

interface ExercisePageClientProps {
  exerciseId: number
}

export function ExercisePageClient({ exerciseId }: ExercisePageClientProps) {
  const router = useRouter()
  const [isInitializing, setIsInitializing] = useState(true)
  const [loadingError, setLoadingError] = useState<Error | null>(null)
  const {
    todaysWorkout,
    isLoadingWorkout,
    workoutError,
    startWorkout,
    exercises,
    workoutSession,
    updateExerciseWorkSets,
  } = useWorkout()
  const { setCurrentExerciseById, loadingStates } = useWorkoutStore()

  useEffect(() => {
    async function initializeWorkout() {
      try {
        setLoadingError(null)

        // If workout is not started and we have workout data, start it
        if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
          const workoutGroup = todaysWorkout[0]
          const workout = workoutGroup?.WorkoutTemplates?.[0]

          if (workout) {
            // Start the workout to populate exercises in the store
            await startWorkout(todaysWorkout)
          } else {
            // No workout available, redirect to workout page
            router.replace('/workout')
            return
          }
        }

        // Set current exercise by ID
        if (exerciseId) {
          setCurrentExerciseById(exerciseId)

          // Pre-load recommendation if not already loaded
          const exercise = exercises?.find((ex) => ex.Id === exerciseId)
          if (exercise && (!exercise.sets || exercise.sets.length === 0)) {
            // Reset loading state to trigger a fresh load
            updateExerciseWorkSets(exerciseId, [])
          }
        }
      } catch (error) {
        console.error('Failed to initialize exercise:', error)
        setLoadingError(
          error instanceof Error ? error : new Error('Failed to load exercise')
        )
      } finally {
        setIsInitializing(false)
      }
    }

    initializeWorkout()
  }, [
    exerciseId,
    todaysWorkout,
    workoutSession,
    isLoadingWorkout,
    startWorkout,
    setCurrentExerciseById,
    exercises,
    updateExerciseWorkSets,
    router,
  ])

  // Show error state with retry option
  if (loadingError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
        <p className="text-red-600 mb-4">
          Failed to load exercise: {loadingError.message}
        </p>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-lg mb-2"
          onClick={() => {
            setIsInitializing(true)
            setLoadingError(null)
            // Re-trigger initialization
            const initializeWorkout = async () => {
              try {
                setLoadingError(null)

                // If workout is not started and we have workout data, start it
                if (!workoutSession && todaysWorkout && !isLoadingWorkout) {
                  const workoutGroup = todaysWorkout[0]
                  const workout = workoutGroup?.WorkoutTemplates?.[0]

                  if (workout) {
                    // Start the workout to populate exercises in the store
                    await startWorkout(todaysWorkout)
                  } else {
                    // No workout available, redirect to workout page
                    router.replace('/workout')
                    return
                  }
                }

                // Set current exercise by ID
                if (exerciseId) {
                  setCurrentExerciseById(exerciseId)

                  // Pre-load recommendation if not already loaded
                  const exercise = exercises?.find((ex) => ex.Id === exerciseId)
                  if (
                    exercise &&
                    (!exercise.sets || exercise.sets.length === 0)
                  ) {
                    // Reset loading state to trigger a fresh load
                    updateExerciseWorkSets(exerciseId, [])
                  }
                }
              } catch (error) {
                console.error('Failed to initialize exercise:', error)
                setLoadingError(
                  error instanceof Error
                    ? error
                    : new Error('Failed to load exercise')
                )
              } finally {
                setIsInitializing(false)
              }
            }
            initializeWorkout()
          }}
        >
          Retry
        </button>
        <button
          className="px-4 py-2 bg-gray-600 text-white rounded-lg"
          onClick={() => router.push('/workout')}
        >
          Back to Workout
        </button>
      </div>
    )
  }

  // Handle workout errors
  if (workoutError) {
    return (
      <div className="flex items-center justify-center min-h-[100dvh] bg-gray-50">
        <div className="text-center p-6 max-w-md">
          <h2 className="text-2xl font-bold text-red-600 mb-4">
            Failed to Load Workout
          </h2>
          <p className="text-gray-600 mb-6">
            {typeof workoutError === 'string'
              ? workoutError
              : workoutError.message || 'Unable to load workout data.'}
          </p>
          <button
            onClick={() => router.push('/workout')}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors min-h-[44px]"
          >
            Back to Workout
          </button>
        </div>
      </div>
    )
  }

  // Check if we're still loading recommendations for this exercise
  const isLoadingRecommendation = exerciseId
    ? loadingStates.get(exerciseId)
    : false

  // Show loading while initializing
  if (isInitializing || isLoadingWorkout || isLoadingRecommendation) {
    return <SetScreenLoadingState />
  }

  // Render the SetScreen once everything is ready
  return <SetScreen exerciseId={exerciseId} />
}
