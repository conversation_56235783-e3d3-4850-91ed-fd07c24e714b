import { render, screen, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import { ExercisePageClient } from '../ExercisePageClient'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import type { WorkoutTemplateGroupModel, ExerciseWorkSetsModel } from '@/types'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')

describe('ExercisePageClient - Direct Navigation', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockStartWorkout = vi.fn()
  const mockSetCurrentExerciseById = vi.fn()
  const mockGetCachedExerciseRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()

  const mockWorkoutGroup: WorkoutTemplateGroupModel[] = [
    {
      Id: 1,
      Label: 'Push Day',
      WorkoutTemplates: [
        {
          Id: 100,
          Label: 'Chest & Triceps',
          IsSystemExercise: true,
          UserId: '',
          Exercises: [
            {
              Id: 1001,
              Label: 'Bench Press',
              IsBodyweight: false,
              IsEasy: false,
              IsSystemExercise: true,
              IsSwappable: true,
              IsTimeBased: false,
              IsUnilateral: false,
              VideoUrl: '',
              EquipmentModel: { Id: 1, Label: 'Barbell' },
              LastTimeModifiedBy: new Date(),
              Minutes: 2,
              Reps: 8,
              Sets: 3,
              SetsModel: {
                Series: '3',
                Reps: '8',
                SetStyle: 'Normal',
              },
              BodyPartId: 1,
              SelectedEquipmentModelId: 1,
              UnilateralOption: 0,
              SelectedUnilateralOption: 0,
            },
          ],
          WorkoutSettingsModel: {},
        },
      ],
    },
  ]

  const mockExercises: ExerciseWorkSetsModel[] = [
    {
      Id: 1001,
      Label: 'Bench Press',
      IsBodyweight: false,
      IsEasy: false,
      IsSystemExercise: true,
      IsSwappable: true,
      IsTimeBased: false,
      IsUnilateral: false,
      VideoUrl: '',
      EquipmentModel: { Id: 1, Label: 'Barbell' },
      LastTimeModifiedBy: new Date(),
      Minutes: 2,
      Reps: 8,
      Sets: 3,
      SetsModel: {
        Series: '3',
        Reps: '8',
        SetStyle: 'Normal',
      },
      BodyPartId: 1,
      SelectedEquipmentModelId: 1,
      UnilateralOption: 0,
      SelectedUnilateralOption: 0,
      WorkSets: [],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkoutStore).mockReturnValue({
      setCurrentExerciseById: mockSetCurrentExerciseById,
      getCachedExerciseRecommendation: mockGetCachedExerciseRecommendation,
      updateExerciseWorkSets: mockUpdateExerciseWorkSets,
      loadingStates: new Map(),
      exerciseRecommendations: {
        1001: {
          Series: 3,
          Reps: 8,
          Weight: { Kg: 80, Lb: 176 },
          WarmUpsList: [],
          RpRest: 180,
          IsBodyweight: false,
        },
      },
    } as any)
  })

  it('should use cached recommendations when navigating directly to exercise', async () => {
    // Mock that workout is started and exercises are loaded
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkoutGroup,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: mockExercises,
      workoutSession: { id: 'test-session' },
    } as any)

    // Mock cached recommendation
    mockGetCachedExerciseRecommendation.mockReturnValue({
      Series: 3,
      Reps: 8,
      Weight: { Kg: 80, Lb: 176 },
    })

    render(<ExercisePageClient exerciseId={1001} />)

    // Should not show loading after initialization
    await waitFor(() => {
      expect(
        screen.queryByText('Loading exercise data...')
      ).not.toBeInTheDocument()
    })

    // Should set current exercise
    expect(mockSetCurrentExerciseById).toHaveBeenCalledWith(1001)

    // Should not redirect
    expect(mockRouter.replace).not.toHaveBeenCalled()
  })

  it('should start workout and load recommendations if not started', async () => {
    // Mock that workout is not started
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkoutGroup,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: null,
      workoutSession: null,
    } as any)

    mockStartWorkout.mockResolvedValueOnce(true)

    render(<ExercisePageClient exerciseId={1001} />)

    // Should attempt to start workout
    await waitFor(() => {
      expect(mockStartWorkout).toHaveBeenCalledWith(mockWorkoutGroup)
    })
  })

  it('should redirect if exercise not found in workout', async () => {
    // Mock exercises without the requested exercise
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkoutGroup,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: [], // Empty exercises
      workoutSession: { id: 'test-session' },
    } as any)

    render(<ExercisePageClient exerciseId={9999} />)

    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith('/workout')
    })
  })

  it('should show loading state while workout is loading', () => {
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: true,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: null,
      workoutSession: null,
    } as any)

    render(<ExercisePageClient exerciseId={1001} />)

    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()
  })
})
