import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { ExercisePageClient } from '../ExercisePageClient'
import { useRouter } from 'next/navigation'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('@/hooks/useWorkout')
vi.mock('@/stores/workoutStore')

describe('Exercise Loading Fixes', () => {
  const mockRouter = {
    replace: vi.fn(),
    push: vi.fn(),
  }

  const mockStartWorkout = vi.fn()
  const mockSetCurrentExerciseById = vi.fn()
  const mockGetCachedExerciseRecommendation = vi.fn()
  const mockUpdateExerciseWorkSets = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useRouter).mockReturnValue(mockRouter as any)
    vi.mocked(useWorkoutStore).mockReturnValue({
      setCurrentExerciseById: mockSetCurrentExerciseById,
      getCachedExerciseRecommendation: mockGetCachedExerciseRecommendation,
      updateExerciseWorkSets: mockUpdateExerciseWorkSets,
      loadingStates: new Map(),
    } as any)
  })

  it('should handle loading errors gracefully', async () => {
    // Mock useWorkout to return error state
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: null,
      workoutSession: null,
    } as any)

    // Mock startWorkout to throw an error
    mockStartWorkout.mockRejectedValueOnce(new Error('Failed to start workout'))

    render(<ExercisePageClient exerciseId={1001} />)

    // Should show error state with retry button
    await waitFor(() => {
      expect(screen.getByText(/Failed to load exercise/)).toBeInTheDocument()
    })

    expect(screen.getByText('Retry')).toBeInTheDocument()
    expect(screen.getByText('Back to Workout')).toBeInTheDocument()
  })

  it('should call updateExerciseWorkSets when initializing exercise', async () => {
    const mockExercises = [
      {
        Id: 1001,
        Label: 'Bench Press',
        recommendation: null,
        sets: [],
      },
    ]

    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: [
        {
          Id: 1,
          Label: 'Push Day',
          WorkoutTemplates: [
            {
              Id: 100,
              Label: 'Chest & Triceps',
              Exercises: mockExercises,
            },
          ],
        },
      ],
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: mockExercises,
      workoutSession: { id: 'test-session' },
    } as any)

    render(<ExercisePageClient exerciseId={1001} />)

    await waitFor(() => {
      expect(mockSetCurrentExerciseById).toHaveBeenCalledWith(1001)
      expect(mockUpdateExerciseWorkSets).toHaveBeenCalledWith(1001, [])
    })
  })

  it('should show loading state initially', () => {
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: true,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: null,
      workoutSession: null,
    } as any)

    render(<ExercisePageClient exerciseId={1001} />)

    expect(screen.getByText('Loading exercise data...')).toBeInTheDocument()
  })

  it('should handle retry functionality', async () => {
    // Mock useWorkout to return error state initially
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: mockStartWorkout,
      exercises: null,
      workoutSession: null,
    } as any)

    // Mock startWorkout to throw an error
    mockStartWorkout.mockRejectedValueOnce(new Error('Network error'))

    render(<ExercisePageClient exerciseId={1001} />)

    // Wait for error state
    await waitFor(() => {
      expect(screen.getByText(/Failed to load exercise/)).toBeInTheDocument()
    })

    // Click retry button
    const retryButton = screen.getByText('Retry')
    expect(retryButton).toBeInTheDocument()

    // The retry functionality should reset the error state
    // This test verifies the retry button exists and can be clicked
    expect(retryButton).toBeEnabled()
  })
})
