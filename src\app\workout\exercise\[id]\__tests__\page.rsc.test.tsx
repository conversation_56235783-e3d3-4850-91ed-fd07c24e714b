import { render } from '@testing-library/react'
import ExercisePage from '../page'
import { ExercisePageClient } from '../ExercisePageClient'

// Mock the ExercisePageClient component
vi.mock('../ExercisePageClient', () => ({
  ExercisePageClient: vi.fn(({ exerciseId }) => (
    <div data-testid="exercise-page-client">Exercise ID: {exerciseId}</div>
  )),
}))

describe('ExercisePage RSC', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render without async/await pattern', () => {
    // Given: A params object with id (not a Promise)
    const params = { id: '3216' }

    // When: Rendering the page
    const { getByTestId } = render(<ExercisePage params={params} />)

    // Then: Should render the client component with correct ID
    expect(getByTestId('exercise-page-client')).toBeInTheDocument()
    expect(ExercisePageClient).toHaveBeenCalledWith(
      { exerciseId: 3216 },
      undefined
    )
  })

  it('should handle string to number conversion', () => {
    // Given: A params object with string id
    const params = { id: '123' }

    // When: Rendering the page
    render(<ExercisePage params={params} />)

    // Then: Should convert string to number
    expect(ExercisePageClient).toHaveBeenCalledWith(
      { exerciseId: 123 },
      undefined
    )
  })

  it('should handle invalid id gracefully', () => {
    // Given: A params object with invalid id
    const params = { id: 'invalid' }

    // When: Rendering the page
    render(<ExercisePage params={params} />)

    // Then: Should pass NaN (parseInt('invalid') returns NaN)
    expect(ExercisePageClient).toHaveBeenCalledWith(
      { exerciseId: NaN },
      undefined
    )
  })
})
