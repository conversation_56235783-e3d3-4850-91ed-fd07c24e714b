import { ExercisePageClient } from './ExercisePageClient'
import { ErrorBoundary } from '@/components/ErrorBoundary'

// Ensure this route is always rendered dynamically on the server so that
// arbitrary exercise IDs work in production (otherwise it could be treated
// as a static route at build time and return 404 for unknown ids).
export const dynamic = 'force-dynamic'

interface ExercisePageProps {
  params: Promise<{
    id: string
  }>
}

function ExerciseErrorFallback({
  error,
  retry,
}: {
  error: Error
  retry: () => void
}) {
  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh] p-4">
      <p className="text-red-600 mb-4">Something went wrong: {error.message}</p>
      <button
        className="px-4 py-2 bg-blue-600 text-white rounded-lg mb-2"
        onClick={retry}
      >
        Try Again
      </button>
      <button
        className="px-4 py-2 bg-gray-600 text-white rounded-lg"
        onClick={() => {
          window.location.href = '/workout'
        }}
      >
        Back to Workout
      </button>
    </div>
  )
}

export default async function ExercisePage({ params }: ExercisePageProps) {
  const { id } = await params
  return (
    <ErrorBoundary fallback={ExerciseErrorFallback}>
      <ExercisePageClient exerciseId={parseInt(id)} />
    </ErrorBoundary>
  )
}
