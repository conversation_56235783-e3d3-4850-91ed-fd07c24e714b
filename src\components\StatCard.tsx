'use client'

import React from 'react'

export interface StatCardProps {
  /** Icon to display */
  icon: React.ReactNode
  /** Label for the stat */
  label: string
  /** Value to display */
  value: string | number
  /** Optional formatting function for the value */
  format?: (value: string | number) => string
  /** Trend indicator */
  trend?: 'up' | 'down'
  /** Loading state */
  isLoading?: boolean
  /** Additional CSS classes */
  className?: string
}

export function StatCard({
  icon,
  label,
  value,
  format,
  trend,
  isLoading = false,
  className = '',
}: StatCardProps) {
  const displayValue = format ? format(value) : value

  if (isLoading) {
    return (
      <div
        data-testid="stat-card-skeleton"
        className={`bg-bg-secondary rounded-theme p-4 min-h-[100px] animate-pulse shimmer ${className}`}
      >
        <div className="flex items-start justify-between">
          <div className="w-10 h-10 bg-bg-tertiary rounded-full" />
          {trend && <div className="w-6 h-6 bg-bg-tertiary rounded" />}
        </div>
        <div className="mt-3 space-y-2">
          <div className="h-4 w-20 bg-bg-tertiary rounded" />
          <div className="h-6 w-16 bg-bg-tertiary rounded" />
        </div>
      </div>
    )
  }

  return (
    <div
      data-testid="stat-card"
      className={`bg-gradient-overlay-subtle bg-bg-secondary rounded-theme p-4 min-h-[100px] shadow-theme-md hover:shadow-theme-lg transition-all duration-300 border border-brand-primary/10 hover:border-brand-primary/20 ${className}`}
    >
      <div className="flex items-start justify-between">
        <div className="text-brand-primary">{icon}</div>
        {trend && (
          <div data-testid="trend-icon">
            {trend === 'up' ? (
              <svg
                className="w-5 h-5 text-green-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                />
              </svg>
            ) : (
              <svg
                className="w-5 h-5 text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
                />
              </svg>
            )}
          </div>
        )}
      </div>
      <div className="mt-3">
        <p className="text-sm font-medium text-text-secondary tracking-luxury">
          {label}
        </p>
        <p className="text-2xl font-bold text-text-primary font-heading tracking-luxury-wide text-shadow-sm">
          {displayValue}
        </p>
      </div>
    </div>
  )
}
