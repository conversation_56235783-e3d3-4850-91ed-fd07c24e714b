'use client'

import React from 'react'
import { RecoveryProgress } from './RecoveryProgress'
import type { RecoveryInfo } from '@/utils/recoveryCalculations'

interface WelcomeCardProps {
  userName?: string
  recoveryInfo: RecoveryInfo
  programName?: string
  nextWorkoutName?: string
  onStartWorkout: () => void
  onAcknowledgeRest: () => void
  isLoading?: boolean
}

export function WelcomeCard({
  userName = 'Champion',
  recoveryInfo,
  programName,
  nextWorkoutName,
  onStartWorkout,
  onAcknowledgeRest,
  isLoading = false,
}: WelcomeCardProps) {
  if (isLoading) {
    return (
      <div className="bg-bg-secondary rounded-theme shadow-theme-md p-6 mx-4 animate-pulse shimmer">
        <div className="h-6 bg-bg-tertiary rounded w-3/4 mb-4" />
        <div className="flex items-center justify-between mb-4">
          <div className="h-20 w-20 bg-bg-tertiary rounded-full" />
          <div className="flex-1 ml-4">
            <div className="h-4 bg-bg-tertiary rounded w-full mb-2" />
            <div className="h-4 bg-bg-tertiary rounded w-2/3" />
          </div>
        </div>
        <div className="h-12 bg-bg-tertiary rounded" />
      </div>
    )
  }

  const { percentage, message, isReady, remainingTime } = recoveryInfo

  return (
    <div
      className="bg-gradient-premium bg-bg-secondary rounded-theme shadow-theme-lg p-6 mx-4 border border-brand-primary/10"
      data-testid="welcome-card"
    >
      <h2 className="text-xl font-heading font-semibold text-text-primary mb-4 tracking-luxury-wide text-shadow-md">
        Welcome back, {userName}!
      </h2>

      <div className="flex items-center justify-between mb-4">
        <RecoveryProgress percentage={percentage} size={80} strokeWidth={8} />
        <div className="flex-1 ml-4">
          <p className="text-sm text-text-secondary">
            {isReady ? 'You are ready to train!' : `Rest for ${remainingTime}`}
          </p>
          <p className="text-lg font-medium text-text-primary mt-1 tracking-luxury">
            Coach says: {message}
          </p>
        </div>
      </div>

      {programName && (
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Your program: <span className="font-medium">{programName}</span>
          </p>
          {nextWorkoutName && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Next: <span className="font-medium">{nextWorkoutName}</span>
            </p>
          )}
        </div>
      )}

      <button
        onClick={isReady ? onStartWorkout : onAcknowledgeRest}
        className="w-full min-h-[44px] bg-gradient-metallic-gold text-text-inverse font-semibold rounded-theme shadow-theme-md hover:shadow-theme-lg transition-all duration-300 shimmer-hover text-shadow-sm"
      >
        {isReady ? 'Start Workout' : 'Got it, Coach!'}
      </button>
    </div>
  )
}
