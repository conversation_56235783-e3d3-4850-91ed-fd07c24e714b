import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ProgramStatsGrid } from '../ProgramStatsGrid'
import type { ProgramStats } from '@/types'
import React from 'react'

describe('ProgramStatsGrid', () => {
  // Removed unused mockProgress - stats are now provided directly
  // const mockProgress: ProgramProgress = {
  //   percentage: 75,
  //   daysCompleted: 45,
  //   totalWorkouts: 60,
  //   currentWeek: 7,
  //   workoutsThisWeek: 2,
  //   remainingWorkouts: 15,
  // }

  const mockStats: ProgramStats = {
    averageWorkoutTime: 45.5,
    totalVolume: 125000,
    personalRecords: 8,
    consecutiveWeeks: 6,
    lastWorkoutDate: '2024-01-15',
    totalWorkoutsCompleted: 45,
    bodyWeight: 180,
    recoveryDays: 2,
    coachRecommendation: 'Great job!',
  }

  describe('Basic rendering', () => {
    it('should render all stat cards with data', () => {
      render(<ProgramStatsGrid stats={mockStats} />)

      // Weeks streak card
      expect(screen.getByText('Weeks streak')).toBeInTheDocument()
      expect(screen.getByText('6')).toBeInTheDocument()

      // Workouts done card
      expect(screen.getByText('Workouts done')).toBeInTheDocument()
      expect(screen.getByText('45')).toBeInTheDocument()

      // Lbs lifted card
      expect(screen.getByText('Lbs lifted')).toBeInTheDocument()
      expect(screen.getByText('125K')).toBeInTheDocument()

      // Body weight card
      expect(screen.getByText('Body weight')).toBeInTheDocument()
      expect(screen.getByText('180')).toBeInTheDocument()

      // Recovery card
      expect(screen.getByText('Recovery')).toBeInTheDocument()
      expect(screen.getByText('2 days')).toBeInTheDocument()

      // Coach says card
      expect(screen.getByText('Coach says')).toBeInTheDocument()
      expect(screen.getByText('Great job!')).toBeInTheDocument()
    })

    it('should handle zero values gracefully', () => {
      // Removed unused zeroProgress - stats are now provided directly

      const zeroStats: ProgramStats = {
        averageWorkoutTime: 0,
        totalVolume: 0,
        personalRecords: 0,
        consecutiveWeeks: 0,
        totalWorkoutsCompleted: 0,
      }

      render(<ProgramStatsGrid stats={zeroStats} />)

      expect(screen.getAllByText('0')).toHaveLength(3) // consecutiveWeeks, totalWorkoutsCompleted, totalVolume
      expect(screen.getAllByText('-')).toHaveLength(3) // bodyWeight, recoveryDays, coachRecommendation are undefined
    })

    it('should show loading state', () => {
      render(<ProgramStatsGrid stats={mockStats} isLoading />)

      const skeletons = screen.getAllByTestId('stat-card-skeleton')
      expect(skeletons).toHaveLength(6)
    })

    it('should handle missing data', () => {
      render(<ProgramStatsGrid stats={undefined} />)

      // Should show dashes for missing data
      expect(screen.getAllByText('-')).toHaveLength(6)
    })
  })

  describe('Grid layout', () => {
    it('should have responsive grid layout', () => {
      render(<ProgramStatsGrid stats={mockStats} />)

      const grid = screen.getByTestId('program-stats-grid')
      expect(grid).toHaveClass('grid')
      expect(grid).toHaveClass('grid-cols-3') // 3 columns
    })

    it('should have proper spacing', () => {
      render(<ProgramStatsGrid stats={mockStats} />)

      const grid = screen.getByTestId('program-stats-grid')
      expect(grid).toHaveClass('gap-3')
    })
  })

  describe('Customization', () => {
    it('should accept custom className', () => {
      render(
        <ProgramStatsGrid stats={mockStats} className="custom-grid-class" />
      )

      const grid = screen.getByTestId('program-stats-grid')
      expect(grid).toHaveClass('custom-grid-class')
    })

    it('should format large numbers', () => {
      const largeStats: ProgramStats = {
        ...mockStats,
        totalVolume: 1234567,
      }

      render(<ProgramStatsGrid stats={largeStats} />)

      // Should format large numbers
      expect(screen.getByText('1M')).toBeInTheDocument() // 1234567 formatted as 1M
    })
  })

  describe('Icon rendering', () => {
    it('should render appropriate icons for each stat', () => {
      render(<ProgramStatsGrid stats={mockStats} />)

      // Check that the component renders (icons will be part of implementation)
      const cards = screen.getAllByTestId('stat-card')
      expect(cards).toHaveLength(6)
    })
  })
})
