import { render, screen, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { QuickSuccessScreen } from '../QuickSuccessScreen'

describe('QuickSuccessScreen', () => {
  beforeEach(() => {
    vi.useFakeTimers()

    // Mock window.matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('renders success icon initially', () => {
    const onComplete = vi.fn()
    render(<QuickSuccessScreen onComplete={onComplete} />)

    expect(screen.getByTestId('quick-success-screen')).toBeInTheDocument()
    expect(screen.getByTestId('success-icon')).toBeInTheDocument()
    expect(screen.queryByText('Welcome back!')).not.toBeInTheDocument()
  })

  it('shows checkmark for 400ms then welcome message', () => {
    const onComplete = vi.fn()
    render(<QuickSuccessScreen onComplete={onComplete} />)

    // Initially shows checkmark
    expect(screen.getByTestId('success-icon')).toBeInTheDocument()
    expect(screen.queryByText('Welcome back!')).not.toBeInTheDocument()

    // At 399ms still shows checkmark
    act(() => {
      vi.advanceTimersByTime(399)
    })
    expect(screen.getByTestId('success-icon')).toBeInTheDocument()
    expect(screen.queryByText('Welcome back!')).not.toBeInTheDocument()

    // At 400ms shows welcome message, hides checkmark
    act(() => {
      vi.advanceTimersByTime(1)
    })
    expect(screen.queryByTestId('success-icon')).not.toBeInTheDocument()
    expect(screen.getByText('Welcome back!')).toBeInTheDocument()
  })

  it('calls onComplete after 800ms', () => {
    const onComplete = vi.fn()
    render(<QuickSuccessScreen onComplete={onComplete} />)

    expect(onComplete).not.toHaveBeenCalled()

    act(() => {
      vi.advanceTimersByTime(799)
    })
    expect(onComplete).not.toHaveBeenCalled()

    act(() => {
      vi.advanceTimersByTime(1)
    })

    // Advance time for the requestAnimationFrame mock (16ms)
    act(() => {
      vi.advanceTimersByTime(16)
    })

    expect(onComplete).toHaveBeenCalledTimes(1)
  })

  it('cleans up timers on unmount', () => {
    const onComplete = vi.fn()
    const { unmount } = render(<QuickSuccessScreen onComplete={onComplete} />)

    unmount()
    vi.advanceTimersByTime(800)

    expect(onComplete).not.toHaveBeenCalled()
  })

  it('uses theme-aware background and text colors', () => {
    const onComplete = vi.fn()
    render(<QuickSuccessScreen onComplete={onComplete} />)

    const container = screen.getByTestId('quick-success-screen')
    expect(container).toHaveClass('bg-bg-primary')
    expect(container).not.toHaveClass('bg-white')
    expect(container).not.toHaveClass('dark:bg-gray-900')

    act(() => {
      vi.advanceTimersByTime(400)
    })

    const welcomeText = screen.getByText('Welcome back!')
    expect(welcomeText).toHaveClass('text-text-primary')
    expect(welcomeText).not.toHaveClass('text-gray-900')
    expect(welcomeText).not.toHaveClass('dark:text-white')
  })
})
