import React from 'react'
import { render } from '@testing-library/react'
import { BicepsIcon } from '../BicepsIcon'

describe('BicepsIcon', () => {
  it('should render without errors', () => {
    const { container } = render(<BicepsIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    const { container } = render(<BicepsIcon className="custom-class" />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveClass('custom-class')
  })

  it('should apply custom size', () => {
    const { container } = render(<BicepsIcon size={32} />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('width', '32')
    expect(svg).toHaveAttribute('height', '32')
  })

  it('should have aria-hidden attribute', () => {
    const { container } = render(<BicepsIcon />)
    const svg = container.querySelector('svg')
    expect(svg).toHaveAttribute('aria-hidden', 'true')
  })

  it('should render biceps path', () => {
    const { container } = render(<BicepsIcon />)
    const path = container.querySelector('path')
    expect(path).toBeInTheDocument()
  })
})
