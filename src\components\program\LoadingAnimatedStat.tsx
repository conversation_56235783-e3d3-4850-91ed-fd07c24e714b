import React, { useEffect, useState, useRef } from 'react'
import { StatCard, StatCardProps } from './StatCard'

interface LoadingAnimatedStatProps extends Omit<StatCardProps, 'value'> {
  /** The actual value from the API */
  value: number
  /** Whether the data is still loading */
  isLoading: boolean
  /** Estimated max value for loading animation */
  estimatedMax?: number
  /** Duration of loading animation in ms */
  loadingDuration?: number
}

export function LoadingAnimatedStat({
  value,
  isLoading,
  estimatedMax = 10,
  loadingDuration = 8000,
  ...statCardProps
}: LoadingAnimatedStatProps) {
  // Initialize with value if we already have it (prevents double animation on mount)
  const [displayValue, setDisplayValue] = useState(
    value > 0 && !isLoading ? value : 0
  )
  const [hasRealData, setHasRealData] = useState(value > 0 && !isLoading)
  const animationStartTime = useRef<number>(Date.now())
  const loadingAnimationRef = useRef<number | undefined>(undefined)
  const hasInitialized = useRef(false)

  // Handle initial mount with real data
  useEffect(() => {
    if (!hasInitialized.current && value > 0 && !isLoading) {
      hasInitialized.current = true
      setHasRealData(true)
      setDisplayValue(value)
    }
  }, [value, isLoading])

  useEffect(() => {
    // If we have real data, stop loading animation and show real value
    if (!isLoading && value > 0) {
      if (loadingAnimationRef.current) {
        cancelAnimationFrame(loadingAnimationRef.current)
      }
      // Set hasRealData but don't update displayValue here
      // Let the finalValue calculation handle the transition
      setHasRealData(true)
      return
    }

    // Start loading animation only if still loading and we don't have real data yet
    if (isLoading && !hasRealData && value === 0) {
      const animate = () => {
        const elapsed = Date.now() - animationStartTime.current
        const progress = Math.min(elapsed / loadingDuration, 1)

        // Use a very slow ease-in curve for loading animation (quartic)
        const easeIn = progress * progress * progress * progress
        const animatedValue = Math.round(estimatedMax * easeIn)

        setDisplayValue(animatedValue)

        // Continue animation if still loading
        if (progress < 1 && isLoading) {
          loadingAnimationRef.current = requestAnimationFrame(animate)
        }
      }

      loadingAnimationRef.current = requestAnimationFrame(animate)
    }

    return () => {
      if (loadingAnimationRef.current) {
        cancelAnimationFrame(loadingAnimationRef.current)
      }
    }
  }, [isLoading, value, hasRealData, estimatedMax, loadingDuration])

  // When we have real data, pass the real value directly
  // This prevents double animation by immediately showing the real value
  // instead of animating from the current loading value
  const finalValue =
    hasRealData || (!isLoading && value > 0) ? value : displayValue

  return (
    <StatCard
      {...statCardProps}
      value={finalValue}
      isLoading={false} // Never show shimmer
      showShimmer={false}
    />
  )
}
