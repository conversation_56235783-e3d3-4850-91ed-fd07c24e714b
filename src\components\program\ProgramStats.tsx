import React from 'react'
import { LoadingAnimatedStat } from './LoadingAnimatedStat'
import { StreakIcon, WorkoutsIcon, BicepsIcon } from '@/components/icons'
import type { UserStats } from '@/types'

interface ProgramStatsProps {
  stats: UserStats
  isLoadingStats: boolean
  showStatsLoaded: boolean
}

export function ProgramStats({
  stats,
  isLoadingStats,
  showStatsLoaded,
}: ProgramStatsProps) {
  return (
    <div className="py-6 px-4">
      {/* Loading/Success message - always rendered to prevent layout shift */}
      <p
        className={`text-center mb-4 transition-opacity duration-300 ${
          isLoadingStats || showStatsLoaded ? 'opacity-100' : 'opacity-0'
        } ${
          showStatsLoaded
            ? 'text-brand-accent'
            : 'text-text-secondary animate-pulse'
        }`}
      >
        {showStatsLoaded && 'Loaded'}
        {!showStatsLoaded && isLoadingStats && 'Loading your stats...'}
        {!showStatsLoaded && !isLoadingStats && '\u00A0'}
      </p>
      <div className="">
        <div className="grid grid-cols-1 gap-4">
          {/* Week Streak Card */}
          <LoadingAnimatedStat
            value={stats.weekStreak}
            label="Weeks streak"
            icon={<StreakIcon size={20} className="text-brand-primary" />}
            isLoading={isLoadingStats}
            estimatedMax={8}
            loadingDuration={8000}
            className=""
          />

          {/* Workouts Completed Card */}
          <LoadingAnimatedStat
            value={stats.workoutsCompleted}
            label="Workouts done"
            icon={<WorkoutsIcon size={20} className="text-brand-accent" />}
            isLoading={isLoadingStats}
            estimatedMax={30}
            loadingDuration={8000}
            className=""
          />

          {/* Lbs Lifted Card */}
          <LoadingAnimatedStat
            value={stats.lbsLifted}
            label="Lbs lifted"
            icon={<BicepsIcon size={20} className="text-brand-secondary" />}
            isLoading={isLoadingStats}
            estimatedMax={10000}
            loadingDuration={8000}
            className=""
          />
        </div>
      </div>
    </div>
  )
}
