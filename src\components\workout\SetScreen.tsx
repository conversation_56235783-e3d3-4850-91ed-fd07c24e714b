'use client'

import React, { useEffect } from 'react'
import { useNavigation } from '@/contexts/NavigationContext'
import { useSetScreenLogic } from '@/hooks/useSetScreenLogic'
import { SetInputs } from './SetInputs'
import { RIRPicker } from './RIRPicker'
import { ExerciseCompleteView } from './ExerciseCompleteView'
import { WorkoutCompleteView } from './WorkoutCompleteView'
import { SetScreenLoadingState } from './SetScreenLoadingState'
import { SetScreenErrorState } from './SetScreenErrorState'
import { SetProgressInfo } from './SetProgressInfo'
import { ExerciseInfo } from './ExerciseInfo'
import { SaveErrorDisplay } from './SaveErrorDisplay'

interface SetScreenProps {
  exerciseId?: number
}

export function SetScreen({ exerciseId }: SetScreenProps) {
  const { setTitle } = useNavigation()
  const {
    // State
    currentExercise,
    exercises,
    currentExerciseIndex,
    isWarmup,
    totalSets,
    currentSetIndex,
    setData,
    isSaving,
    saveError,
    showRIRPicker,
    showComplete,
    showExerciseComplete,
    isTransitioning,
    showSetSaved,
    recommendation,
    isLoading,
    error,
    isLastExercise,

    // Actions
    setSetData,
    handleSaveSet,
    handleRIRSelect,
    handleRIRCancel,
    refetchRecommendation,
    performancePercentage,
  } = useSetScreenLogic(exerciseId)

  // Update navigation title with exercise name
  useEffect(() => {
    if (currentExercise) {
      setTitle(currentExercise.Label)
    }
  }, [currentExercise, setTitle])

  // Log state for debugging set index issue
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('[SetScreen] Component mounted/updated:', {
        exerciseId,
        currentSetIndex,
        totalSets,
        currentExerciseIndex,
        currentExerciseLabel: currentExercise?.Label,
      })
    }
  }, [
    exerciseId,
    currentSetIndex,
    totalSets,
    currentExerciseIndex,
    currentExercise,
  ])

  const getErrorMessage = (error: string | Error): string => {
    if (typeof error === 'string') {
      if (error === 'Too many requests. Please wait.') {
        return 'Too many requests. Please wait a moment and try again.'
      }
      if (error === 'Invalid response format') {
        return 'Error loading workout data. Please try again.'
      }
      return error
    }

    if (error instanceof Error) {
      return error.message
    }

    return 'An error occurred. Please try again.'
  }

  // Loading state
  if (isLoading && !recommendation) {
    return <SetScreenLoadingState />
  }

  // Error state
  if (error && !recommendation) {
    return <SetScreenErrorState onRetry={refetchRecommendation} />
  }

  // Exercise complete state
  if (showExerciseComplete) {
    return (
      <ExerciseCompleteView
        exerciseLabel={currentExercise?.Label}
        isLastExercise={isLastExercise}
        nextExerciseLabel={
          exercises && typeof currentExerciseIndex === 'number'
            ? exercises[currentExerciseIndex + 1]?.Label
            : undefined
        }
      />
    )
  }

  // Workout complete state
  if (showComplete) {
    return <WorkoutCompleteView />
  }

  return (
    <div className="min-h-[100dvh] bg-bg-primary">
      {/* Error Display */}
      {error && (
        <div className="bg-error/10 border-b border-error/20 px-4 py-3">
          <p className="text-error text-sm">{getErrorMessage(error)}</p>
        </div>
      )}

      {/* Set Progress Info */}
      <SetProgressInfo
        isWarmup={isWarmup}
        currentSetIndex={currentSetIndex}
        totalSets={totalSets}
        showSetSaved={showSetSaved}
      />

      {/* Exercise Info */}
      <ExerciseInfo
        currentExercise={currentExercise}
        recommendation={recommendation}
        performancePercentage={performancePercentage()}
      />

      {/* Set Inputs */}
      <div className="px-4 py-6">
        <SetInputs
          reps={setData.reps}
          weight={setData.weight}
          duration={setData.duration}
          unit="lbs"
          onChange={(data) => setSetData((prev) => ({ ...prev, ...data }))}
          disabled={isSaving || isTransitioning}
          isBodyweight={currentExercise?.IsBodyweight}
          isTimeBased={currentExercise?.IsTimeBased}
        />
      </div>

      {/* Save error */}
      {saveError && (
        <SaveErrorDisplay error={saveError} onRetry={handleSaveSet} />
      )}

      {/* Save Button */}
      <div className="px-4 pb-6">
        <button
          onClick={handleSaveSet}
          disabled={isSaving || isTransitioning}
          type="submit"
          className={`w-full py-4 rounded-theme font-medium text-lg transition-colors ${
            isSaving || isTransitioning
              ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed'
              : 'bg-brand-primary text-text-inverse hover:bg-brand-primary/90'
          }`}
        >
          {(() => {
            if (isSaving) return 'Saving...'
            if (isTransitioning) return 'Loading next set...'
            return 'Save Set'
          })()}
        </button>
      </div>

      {/* RIR Picker */}
      <RIRPicker
        isOpen={showRIRPicker}
        onSelect={handleRIRSelect}
        onCancel={handleRIRCancel}
      />
    </div>
  )
}
