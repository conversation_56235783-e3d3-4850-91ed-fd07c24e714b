'use client'

import { useEffect } from 'react'
import { useSuccessAnimation } from '@/hooks/useSuccessAnimation'
import type { SuccessAnimationOptions } from '@/types/animations'

interface SuccessIconProps {
  /** Size of the icon in pixels */
  size?: number
  /** Color of the icon */
  color?: string
  /** Additional CSS classes */
  className?: string
  /** Whether to start animation automatically on mount */
  autoPlay?: boolean
  /** Callback when animation completes */
  onAnimationComplete?: () => void
  /** Animation options to pass to the hook */
  animationOptions?: SuccessAnimationOptions
}

export function SuccessIcon({
  size = 120,
  color = '#10b981',
  className = '',
  autoPlay = true,
  onAnimationComplete,
  animationOptions,
}: SuccessIconProps) {
  const { state, isAnimating, prefersReducedMotion, start } =
    useSuccessAnimation({
      ...animationOptions,
      onComplete: onAnimationComplete,
    })

  useEffect(() => {
    if (autoPlay) {
      start()
    }
  }, [autoPlay, start])

  const shouldAnimate =
    !prefersReducedMotion && (state === 'active' || state === 'entering')

  return (
    <div
      data-testid="success-icon"
      className={`
        inline-flex items-center justify-center
        ${shouldAnimate ? 'animate-scale-bounce' : ''}
        ${isAnimating ? 'will-animate' : ''}
        ${className}
      `}
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 120 120"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-label="Success checkmark"
      >
        {/* Circle background */}
        <circle
          data-testid="success-circle"
          cx="60"
          cy="60"
          r="56"
          stroke={color}
          strokeWidth="10"
          fill="none"
          className={`
            transition-all duration-300
            ${state === 'complete' ? 'opacity-100' : 'opacity-0'}
          `}
        />

        {/* Checkmark path */}
        <path
          data-testid="success-checkmark"
          d="M30 60 L50 80 L90 40"
          stroke={color}
          strokeWidth="12"
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
          className={`
            ${shouldAnimate ? 'animate-checkmark-draw' : ''}
            ${state === 'complete' || prefersReducedMotion ? 'opacity-100' : ''}
          `}
        />
      </svg>
    </div>
  )
}
