'use client'

import { useState, useEffect } from 'react'
import { useWorkout } from '@/hooks/useWorkout'
import { ExerciseTimer } from './ExerciseTimer'
import { SetLoggingModal } from './SetLoggingModal'
import { ExerciseSwapModal } from './ExerciseSwapModal'
import { WorkoutSuccessScreen } from './WorkoutSuccessScreen'
import { clearBadWeightData } from '@/utils/clearBadWeightData'
import type { RecommendationModel } from '@/types'

export function WorkoutScreen() {
  const {
    todaysWorkout,
    currentWorkout,
    currentExercise,
    currentSetIndex,
    workoutSession,
    isLoadingWorkout,
    isLoading,
    workoutError,
    error,
    saveSet,
    startWorkout,
    finishWorkout,
    nextSet,
    goToNextExercise,
    getRecommendation,
    loadAllExerciseRecommendations,
    isOffline,
  } = useWorkout()

  const [isSetModalOpen, setIsSetModalOpen] = useState(false)
  const [isSwapModalOpen, setIsSwapModalOpen] = useState(false)
  const [recommendation, setRecommendation] =
    useState<RecommendationModel | null>(null)
  const [saveError, setSaveError] = useState<string | null>(null)
  const [savedOfflineMessage, setSavedOfflineMessage] = useState(false)
  const [showSuccess, setShowSuccess] = useState(false)
  const [hasLoadedAllRecommendations, setHasLoadedAllRecommendations] =
    useState(false)

  // Load all exercise recommendations when workout page loads
  useEffect(() => {
    if (currentWorkout && !hasLoadedAllRecommendations) {
      loadAllExerciseRecommendations()
      setHasLoadedAllRecommendations(true)
    }
  }, [
    currentWorkout,
    loadAllExerciseRecommendations,
    hasLoadedAllRecommendations,
  ])

  // Clear bad weight data on component mount (one-time fix)
  useEffect(() => {
    clearBadWeightData()
  }, [])

  // Fetch recommendation when exercise changes
  useEffect(() => {
    if (currentExercise) {
      getRecommendation(currentExercise.Id).then(setRecommendation)
    }
  }, [currentExercise, getRecommendation])

  // Handle loading state
  if (isLoadingWorkout) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center p-4">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent" />
          <p className="text-gray-600">Loading workout...</p>
        </div>
      </div>
    )
  }

  // Handle error state
  if (workoutError || error) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center p-4">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold text-red-600">
            Error loading workout
          </h2>
          <p className="text-gray-600">
            {workoutError instanceof Error
              ? workoutError.message
              : error || 'Failed to load'}
          </p>
        </div>
      </div>
    )
  }

  // Handle no workout state
  if (!todaysWorkout || todaysWorkout.length === 0 || !currentWorkout) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center p-4">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-semibold">No workout scheduled</h2>
          <p className="text-gray-600">Check back tomorrow!</p>
        </div>
      </div>
    )
  }

  // Calculate exercise progress
  const totalExercises = currentWorkout.Exercises?.length || 0
  const currentExerciseNumber =
    currentWorkout?.Exercises?.findIndex(
      (ex) => ex.Id === currentExercise?.Id
    ) ?? -1
  const adjustedExerciseNumber = currentExerciseNumber + 1

  const handleStartWorkout = () => {
    if (todaysWorkout) {
      startWorkout(todaysWorkout)
    }
  }

  const handleLogSet = () => {
    setIsSetModalOpen(true)
    setSaveError(null)
    setSavedOfflineMessage(false)
  }

  const handleSaveSet = async (setData: {
    weight: number
    reps: number
    rir?: number
    isWarmup: boolean
    notes: string
  }) => {
    try {
      // Modal returns weight in kg (as entered by user)
      // Convert kg to lbs for the API
      await saveSet({
        exerciseId: currentExercise!.Id,
        weight: setData.weight * 2.20462, // Convert kg to lbs
        reps: setData.reps,
        isWarmup: setData.isWarmup,
        RIR: setData.rir,
      })

      if (isOffline) {
        setSavedOfflineMessage(true)
        setTimeout(() => setSavedOfflineMessage(false), 3000)
      }

      setIsSetModalOpen(false)
      nextSet()
    } catch (error) {
      setSaveError('Failed to save set')
    }
  }

  const handleSkipExercise = () => {
    goToNextExercise()
  }

  const handleSwapExercise = () => {
    setIsSwapModalOpen(true)
  }

  const handleFinishWorkout = async () => {
    try {
      await finishWorkout()
      setShowSuccess(true)
    } catch (error) {
      // Handle error silently
    }
  }

  const hasCompletedSets = workoutSession?.exercises.some(
    (ex) => ex.sets.length > 0
  )

  // Calculate completed exercises
  const completedExerciseCount =
    workoutSession?.exercises.filter((ex) => ex.sets.length > 0).length || 0

  // Show success screen
  if (showSuccess) {
    return <WorkoutSuccessScreen exerciseCount={completedExerciseCount} />
  }

  // Pre-workout state
  if (!workoutSession) {
    return (
      <div className="min-h-[100dvh] bg-gray-50 p-4">
        <div className="mx-auto max-w-lg">
          <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h1 className="mb-2 text-2xl font-bold">{currentWorkout.Label}</h1>
            <p className="mb-4 text-gray-600">{totalExercises} exercises</p>
            <p className="text-sm text-gray-500">Ready to start your workout</p>
          </div>

          <button
            onClick={handleStartWorkout}
            className="w-full rounded-lg bg-blue-600 py-4 text-lg font-semibold text-white hover:bg-blue-700"
          >
            Start Workout
          </button>
        </div>
      </div>
    )
  }

  // Active workout state
  return (
    <div className="min-h-[100dvh] bg-gray-50">
      {/* Header */}
      <div className="bg-white p-4 shadow-sm">
        <div className="mx-auto max-w-lg">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold">{currentWorkout.Label}</h1>
            {isOffline && (
              <span className="rounded-full bg-yellow-100 px-3 py-1 text-sm text-yellow-800">
                Offline Mode
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600">
            Exercise {adjustedExerciseNumber} of {totalExercises}
          </p>
        </div>
      </div>

      {/* Timer */}
      <div className="mx-auto max-w-lg p-4">
        <ExerciseTimer
          restDuration={90}
          onRestComplete={() => {}}
          workoutStartTime={workoutSession.startTime}
          isExercising={!isSetModalOpen}
        />
      </div>

      {/* Current Exercise */}
      {currentExercise && (
        <div className="mx-auto max-w-lg px-4">
          <div className="mb-6 rounded-lg bg-white p-6 shadow-sm">
            <h2 className="mb-2 text-xl font-bold">{currentExercise.Label}</h2>
            <p className="mb-4 text-gray-600">Set {currentSetIndex + 1}</p>

            <div className="mb-4 space-y-2">
              <p className="text-sm text-gray-600">Target: 8-12 reps</p>
              {recommendation && (
                <>
                  <p className="text-sm font-medium text-blue-600">
                    Recommendation:{' '}
                    {recommendation.Weight?.Lb > 0
                      ? `${recommendation.Weight.Lb} lbs × ${recommendation.Reps} reps`
                      : `${recommendation.Reps} reps`}
                  </p>
                  {recommendation.RIR && (
                    <p className="text-sm text-blue-600">
                      Target RIR: {recommendation.RIR}
                    </p>
                  )}
                </>
              )}
            </div>

            <div className="flex gap-2">
              <button
                onClick={handleLogSet}
                className="flex-1 rounded-lg bg-blue-600 py-3 text-white hover:bg-blue-700"
              >
                Log Set
              </button>
              <button
                onClick={handleSkipExercise}
                className="rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50"
              >
                Skip Exercise
              </button>
              <button
                onClick={handleSwapExercise}
                className="rounded-lg border border-gray-300 px-4 py-3 text-gray-700 hover:bg-gray-50"
              >
                Swap Exercise
              </button>
            </div>
          </div>

          {/* Action buttons */}
          <button
            onClick={handleFinishWorkout}
            disabled={!hasCompletedSets || isLoading}
            className={`w-full rounded-lg py-4 text-lg font-semibold text-white transition-colors ${
              hasCompletedSets
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-gray-400 cursor-not-allowed'
            } disabled:opacity-50`}
          >
            {hasCompletedSets
              ? 'Finish and save workout'
              : 'Complete at least one set'}
          </button>
        </div>
      )}

      {/* Messages */}
      {savedOfflineMessage && (
        <div className="fixed bottom-4 left-4 right-4 mx-auto max-w-lg rounded-lg bg-yellow-100 p-3 text-center text-sm text-yellow-800">
          Saved offline. Will sync when connection is restored.
        </div>
      )}

      {/* Modals */}
      {currentExercise && (
        <>
          <SetLoggingModal
            isOpen={isSetModalOpen}
            exercise={currentExercise}
            setNumber={currentSetIndex + 1}
            recommendation={recommendation?.Weight || null}
            onSave={handleSaveSet}
            onClose={() => {
              setIsSetModalOpen(false)
              setSaveError(null)
            }}
            error={saveError}
          />

          <ExerciseSwapModal
            isOpen={isSwapModalOpen}
            currentExercise={currentExercise}
            onSwap={() => {
              // TODO: Implement exercise swap
              setIsSwapModalOpen(false)
            }}
            onClose={() => setIsSwapModalOpen(false)}
          />
        </>
      )}
    </div>
  )
}
