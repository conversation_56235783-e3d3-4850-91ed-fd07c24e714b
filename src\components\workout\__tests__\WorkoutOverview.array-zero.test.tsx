import { render } from '@testing-library/react'
import { vi } from 'vitest'
import { WorkoutOverview } from '../WorkoutOverview'
import { mockWorkoutData } from './WorkoutOverview.test.helpers'
import type { UseWorkoutReturn } from './WorkoutOverview.types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout')
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

describe('WorkoutOverview - Array.from zero length', () => {
  const mockUseWorkout = vi.fn<[], UseWorkoutReturn>()

  beforeEach(async () => {
    vi.clearAllMocks()
    const { useWorkout } = vi.mocked(await import('@/hooks/useWorkout'))
    useWorkout.mockImplementation(mockUseWorkout)
  })

  it('should check if Array.from with length 0 somehow renders', () => {
    // This should trigger the skeleton logic with 0 length
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [
                { Id: 1, Label: 'Exercise 1', IsBodyweight: false },
                { Id: 2, Label: 'Exercise 2', IsBodyweight: false },
              ],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 1,
          Label: 'Exercise 1',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
        {
          Id: 2,
          Label: 'Exercise 2',
          BodyPartId: 2,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
      ],
      isLoadingWorkout: true, // Still loading to trigger skeleton logic
      expectedExerciseCount: 2, // Same as loaded exercises
      hasInitialData: true,
      isLoadingFresh: false,
      workoutSession: null,
    })

    const { container } = render(<WorkoutOverview />)

    // expectedExerciseCount (2) - displayExercises.length (2) = 0
    // This should create Array.from({ length: 0 })

    // Check if this calculation (0) is somehow being rendered
    const html = container.innerHTML
    /* eslint-disable no-console */
    console.log('Looking for standalone 0 in HTML...')
    /* eslint-enable no-console */

    // Check for patterns like >0< not inside other text
    const standaloneZeroPattern = />0</
    if (standaloneZeroPattern.test(html)) {
      /* eslint-disable no-console */
      console.log('Found >0< in HTML!')
      // Extract context around the 0
      const match = html.match(/.{50}>0<.{50}/)
      if (match) {
        console.log('Context:', match[0])
      }
      /* eslint-enable no-console */
    }

    // Also log the skeleton section
    const skeletonParent = container.querySelector('.mb-8.space-y-3')
    /* eslint-disable no-console */
    console.log('Skeleton parent HTML:', skeletonParent?.innerHTML)
    /* eslint-enable no-console */
  })

  it('should test the exact calculation that might produce 0', () => {
    // Test the exact math: Math.max(0, expectedExerciseCount - displayExercises.length)
    const expectedExerciseCount = 2
    const displayExercisesLength = 2
    const result = Math.max(0, expectedExerciseCount - displayExercisesLength)

    expect(result).toBe(0)

    // Array.from({ length: 0 }) should produce empty array
    const array = Array.from({ length: result })
    expect(array).toHaveLength(0)

    // This should not render anything
    const elements = array.map((_, i) => `Element ${i}`)
    expect(elements).toHaveLength(0)
  })
})
