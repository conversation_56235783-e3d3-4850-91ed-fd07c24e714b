import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { WorkoutOverview } from '../WorkoutOverview'
import { useWorkout } from '@/hooks/useWorkout'

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout', () => ({
  useWorkout: vi.fn(),
}))

vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

// Mock performance utils
vi.mock('@/utils/performance', () => ({
  PerformanceMonitor: {
    mark: vi.fn(),
    reportKeyMetrics: vi.fn(),
  },
  PerformanceMarks: {
    WORKOUT_PAGE_INTERACTIVE: 'workout-page-interactive',
  },
}))

const mockWorkoutData = [
  {
    Id: 1,
    Label: 'Test Program',
    WorkoutTemplates: [
      {
        Id: 101,
        Label: 'Test Workout',
        Exercises: [
          {
            Id: 201,
            Label: 'Bench Press',
            BodyPartId: 1,
            EquipmentId: 1,
          },
          {
            Id: 202,
            Label: 'Squat',
            BodyPartId: 2,
            EquipmentId: 1,
          },
          {
            Id: 203,
            Label: 'Deadlift',
            BodyPartId: 3,
            EquipmentId: 1,
          },
        ],
      },
    ],
  },
]

describe('WorkoutOverview - Exercise Display', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })
    vi.clearAllMocks()
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    )
  }

  it('should display exercises when workout data is loaded but workout not started', () => {
    // Mock useWorkout to return workout data but no active session
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkoutData,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: [], // Empty because workout not started
      exerciseWorkSetsModels: [], // Empty because workout not started
      expectedExerciseCount: 3,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null, // No active session
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    renderWithProviders(<WorkoutOverview />)

    // Should display exercise names from workout data
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
    expect(screen.getByText('Squat')).toBeInTheDocument()
    expect(screen.getByText('Deadlift')).toBeInTheDocument()

    // Should show start workout button
    expect(screen.getByTestId('start-workout-button')).toHaveTextContent(
      'Start Workout'
    )
  })

  it('should display exercises from exerciseWorkSetsModels when workout is started', () => {
    // Mock useWorkout with active workout session
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: mockWorkoutData,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: mockWorkoutData[0].WorkoutTemplates[0].Exercises,
      exerciseWorkSetsModels: [
        {
          Id: 201,
          Label: 'Bench Press',
          BodyPartId: 1,
          WorkoutSets: [],
          isLoading: false,
          error: null,
          sets: [], // ExerciseCard expects this
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
        },
        {
          Id: 202,
          Label: 'Squat',
          BodyPartId: 2,
          WorkoutSets: [],
          isLoading: false,
          error: null,
          sets: [], // ExerciseCard expects this
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
        },
        {
          Id: 203,
          Label: 'Deadlift',
          BodyPartId: 3,
          WorkoutSets: [],
          isLoading: false,
          error: null,
          sets: [], // ExerciseCard expects this
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
        },
      ],
      expectedExerciseCount: 3,
      hasInitialData: true,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: {
        id: 'test-session',
        startTime: new Date(),
        exercises: [],
      },
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    renderWithProviders(<WorkoutOverview />)

    // Should still display exercises
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
    expect(screen.getByText('Squat')).toBeInTheDocument()
    expect(screen.getByText('Deadlift')).toBeInTheDocument()

    // Should show continue workout button
    expect(screen.getByTestId('start-workout-button')).toHaveTextContent(
      'Continue Workout'
    )
  })

  it('should not display exercises when no workout data is available', () => {
    vi.mocked(useWorkout).mockReturnValue({
      todaysWorkout: null,
      isLoadingWorkout: false,
      workoutError: null,
      startWorkout: vi.fn(),
      userProgramInfo: null,
      exercises: [],
      exerciseWorkSetsModels: [],
      expectedExerciseCount: 0,
      hasInitialData: false,
      isLoadingFresh: false,
      isOffline: false,
      refreshWorkout: vi.fn(),
      updateExerciseWorkSets: vi.fn(),
      workoutSession: null,
      finishWorkout: vi.fn(),
      isLoading: false,
    } as any)

    renderWithProviders(<WorkoutOverview />)

    // Should show no workout available message
    expect(screen.getByText('No Workout Available')).toBeInTheDocument()

    // Should not show any exercises
    expect(screen.queryByText('Bench Press')).not.toBeInTheDocument()
    expect(screen.queryByText('Squat')).not.toBeInTheDocument()
    expect(screen.queryByText('Deadlift')).not.toBeInTheDocument()
  })
})
