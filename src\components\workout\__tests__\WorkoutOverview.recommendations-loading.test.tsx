import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { WorkoutOverview } from '../WorkoutOverview'
import { mockWorkoutData } from './WorkoutOverview.test.helpers'
import type { UseWorkoutReturn } from './WorkoutOverview.types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout')
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

describe('WorkoutOverview - Recommendations Loading', () => {
  const mockUseWorkout = vi.fn<[], UseWorkoutReturn>()

  beforeEach(async () => {
    vi.clearAllMocks()
    const { useWorkout } = vi.mocked(await import('@/hooks/useWorkout'))
    useWorkout.mockImplementation(mockUseWorkout)
  })

  it('should NOT show "Loading exercise recommendations..." when exercises are loaded but isLoadingFresh is true', () => {
    // Setup: Exercises are loaded, but background refresh is happening
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [
                { Id: 1, Label: 'Bench Press', IsBodyweight: false },
                { Id: 2, Label: 'Squats', IsBodyweight: false },
              ],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 1,
          Label: 'Bench Press',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
        {
          Id: 2,
          Label: 'Squats',
          BodyPartId: 2,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
      ],
      isLoadingWorkout: false,
      hasInitialData: true,
      isLoadingFresh: true, // Background refresh happening
      workoutSession: null,
    })

    render(<WorkoutOverview />)

    // Verify exercises are displayed
    expect(screen.getByText('Bench Press')).toBeInTheDocument()
    expect(screen.getByText('Squats')).toBeInTheDocument()

    // Verify the misleading loading message is NOT shown
    expect(
      screen.queryByText('Loading exercise recommendations...')
    ).not.toBeInTheDocument()
  })

  it('should show exercises without loading indicator when background refresh completes', () => {
    // Setup: Exercises loaded, no background refresh
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [{ Id: 1, Label: 'Bench Press', IsBodyweight: false }],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 1,
          Label: 'Bench Press',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
      ],
      isLoadingWorkout: false,
      hasInitialData: true,
      isLoadingFresh: false, // No background refresh
      workoutSession: null,
    })

    render(<WorkoutOverview />)

    // Verify exercise is displayed
    expect(screen.getByText('Bench Press')).toBeInTheDocument()

    // Verify no loading indicator
    expect(
      screen.queryByText('Loading exercise recommendations...')
    ).not.toBeInTheDocument()
  })
})
