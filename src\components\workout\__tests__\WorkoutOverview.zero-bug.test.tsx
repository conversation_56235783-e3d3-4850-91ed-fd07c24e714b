import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { WorkoutOverview } from '../WorkoutOverview'
import { mockWorkoutData } from './WorkoutOverview.test.helpers'
import type { UseWorkoutReturn } from './WorkoutOverview.types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout')
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

describe('WorkoutOverview - Zero Bug', () => {
  const mockUseWorkout = vi.fn<[], UseWorkoutReturn>()

  beforeEach(async () => {
    vi.clearAllMocks()
    const { useWorkout } = vi.mocked(await import('@/hooks/useWorkout'))
    useWorkout.mockImplementation(mockUseWorkout)
  })

  it('should reproduce the exact scenario from the screenshot', () => {
    // Setup to match screenshot: 2 exercises loaded, but expectedExerciseCount might be 0
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [
                { Id: 1, Label: 'Punching Bag', IsBodyweight: true },
                {
                  Id: 2,
                  Label: 'Cable Overhead Triceps Extension',
                  IsBodyweight: false,
                },
              ],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [], // Empty initially
      exercises: [], // This would make expectedExerciseCount = 0
      isLoadingWorkout: false,
      expectedExerciseCount: 0, // This is the key - it's 0
      hasInitialData: true,
      isLoadingFresh: true,
      workoutSession: null,
    })

    const { container, rerender } = render(<WorkoutOverview />)

    // Now update with exercises loaded
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [
                { Id: 1, Label: 'Punching Bag', IsBodyweight: true },
                {
                  Id: 2,
                  Label: 'Cable Overhead Triceps Extension',
                  IsBodyweight: false,
                },
              ],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 1,
          Label: 'Punching Bag',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: true,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
        {
          Id: 2,
          Label: 'Cable Overhead Triceps Extension',
          BodyPartId: 2,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
      ],
      exercises: [], // Still empty, causing expectedExerciseCount = 0
      isLoadingWorkout: false,
      expectedExerciseCount: 0,
      hasInitialData: true,
      isLoadingFresh: true,
      workoutSession: null,
    })

    rerender(<WorkoutOverview />)

    // Look for any "0" in the document
    const zeroText = screen.queryByText('0')
    const zeroRegex = screen.queryByText(/^0$/)

    /* eslint-disable no-console */
    console.log('Found "0" as text:', !!zeroText)
    console.log('Found "0" with regex:', !!zeroRegex)

    // Check specific areas
    const exerciseCards = screen.getAllByTestId('exercise-card')
    console.log('Number of exercise cards:', exerciseCards.length)

    // Get all text content
    const allTextContent = container.textContent
    console.log('All text includes "0":', allTextContent?.includes('0'))
    /* eslint-enable no-console */

    // Check between exercises
    const mainContent = container.querySelector('.p-4.pb-24')
    if (mainContent) {
      const textNodes = []
      const walker = document.createTreeWalker(
        mainContent,
        NodeFilter.SHOW_TEXT,
        null
      )
      let node
      /* eslint-disable no-cond-assign */
      while ((node = walker.nextNode())) {
        const text = node.textContent?.trim()
        if (text && text === '0') {
          /* eslint-disable no-console */
          console.log('Found text node with "0"')
          /* eslint-enable no-console */
          textNodes.push(node)
        }
      }
      /* eslint-enable no-cond-assign */
    }
  })
})
