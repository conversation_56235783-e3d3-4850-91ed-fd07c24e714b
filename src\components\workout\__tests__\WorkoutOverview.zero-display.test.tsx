import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { WorkoutOverview } from '../WorkoutOverview'
import { mockWorkoutData } from './WorkoutOverview.test.helpers'
import type { UseWorkoutReturn } from './WorkoutOverview.types'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock hooks
vi.mock('@/hooks/useWorkout')
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isRefreshing: false,
    isPulling: false,
  }),
}))

describe('WorkoutOverview - Zero Display Investigation', () => {
  const mockUseWorkout = vi.fn<[], UseWorkoutReturn>()

  beforeEach(async () => {
    vi.clearAllMocks()
    const { useWorkout } = vi.mocked(await import('@/hooks/useWorkout'))
    useWorkout.mockImplementation(mockUseWorkout)
  })

  it('should investigate if "0" is displayed when expectedExerciseCount matches loaded exercises', () => {
    // Setup: All exercises loaded but still in loading state
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [
                { Id: 1, Label: 'Punching Bag', IsBodyweight: true },
                {
                  Id: 2,
                  Label: 'Cable Overhead Triceps Extension',
                  IsBodyweight: false,
                },
              ],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 1,
          Label: 'Punching Bag',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: true,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
        {
          Id: 2,
          Label: 'Cable Overhead Triceps Extension',
          BodyPartId: 2,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
      ],
      isLoadingWorkout: true, // Still loading
      expectedExerciseCount: 2, // Matches loaded exercises
      hasInitialData: true,
      isLoadingFresh: true,
      workoutSession: null,
    })

    const { container } = render(<WorkoutOverview />)

    // Check for any "0" text in the document
    const zeroElements = screen.queryAllByText('0')
    /* eslint-disable no-console */
    console.log('Found zero elements:', zeroElements.length)

    // Also check container HTML for any "0"
    const html = container.innerHTML
    const zeroMatches = html.match(/>0</g) || []
    console.log('HTML contains >0<:', zeroMatches.length, 'times')

    // Log the section between exercises and button
    const exerciseList = container.querySelector('.mb-8.space-y-3')
    if (exerciseList) {
      console.log('Exercise list HTML:', exerciseList.innerHTML)
    }
    /* eslint-enable no-console */
  })

  it('should check if zero skeletons create unexpected output', () => {
    // When expectedExerciseCount - displayExercises.length = 0
    mockUseWorkout.mockReturnValue({
      ...mockWorkoutData,
      todaysWorkout: [
        {
          Id: 1,
          WorkoutTemplates: [
            {
              Id: 1,
              Exercises: [
                { Id: 1, Label: 'Test Exercise', IsBodyweight: false },
              ],
            },
          ],
        },
      ],
      exerciseWorkSetsModels: [
        {
          Id: 1,
          Label: 'Test Exercise',
          BodyPartId: 1,
          IsFinished: false,
          IsNextExercise: false,
          isLoadingSets: false,
          setsError: null,
          lastSetsUpdate: 0,
          sets: [],
          IsBodyweight: false,
          WorkoutSets: [],
          isLoading: false,
          error: null,
        },
      ],
      isLoadingWorkout: true,
      expectedExerciseCount: 1, // Same as loaded
      hasInitialData: true,
      isLoadingFresh: false,
      workoutSession: null,
    })

    const { container } = render(<WorkoutOverview />)

    // The Array.from with length 0 should not create any elements
    const skeletons = container.querySelectorAll('[key*="skeleton"]')
    expect(skeletons).toHaveLength(0)

    // Verify no "0" is displayed
    expect(screen.queryByText('0')).not.toBeInTheDocument()
  })
})
