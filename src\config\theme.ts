/**
 * Dr. Muscle X Theme Configuration
 * Centralized theme settings for easy customization
 */

export const themeConfig = {
  // Default theme
  defaultTheme: 'subtle-depth' as const,

  // Theme definitions
  themes: {
    'subtle-depth': {
      name: 'Subtle Depth',
      description: 'Premium sophistication with rich, layered interfaces',
      colors: {
        bg: {
          primary: '#0a0a0b',
          secondary: '#1a1a1c',
          tertiary: '#2a2a2c',
          overlay: 'rgba(0, 0, 0, 0.7)',
        },
        text: {
          primary: '#ffffff',
          secondary: '#b8b8bc',
          tertiary: '#7a7a7e',
          inverse: '#0a0a0b',
        },
        brand: {
          primary: '#d4af37', // Golden
          secondary: '#f7e98e', // Light gold
          accent: '#fff8dc', // Cream
        },
        semantic: {
          error: '#ef4444',
          warning: '#f59e0b',
          success: '#10b981',
          info: '#3b82f6',
        },
      },
      typography: {
        heading: '"Playfair Display", serif',
        body: '"SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
      },
      shadows: {
        sm: '0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(212, 175, 55, 0.1)',
        md: '0 4px 12px rgba(0, 0, 0, 0.6), 0 2px 4px rgba(212, 175, 55, 0.15)',
        lg: '0 8px 24px rgba(0, 0, 0, 0.7), 0 4px 8px rgba(212, 175, 55, 0.2)',
        xl: '0 16px 48px rgba(0, 0, 0, 0.8), 0 8px 16px rgba(212, 175, 55, 0.25)',
      },
      borders: {
        radius: {
          button: '0.5rem',
          card: '0.75rem',
          modal: '1rem',
        },
      },
    },
    glassmorphism: {
      name: 'Glassmorphism',
      description: 'Modern frosted glass effects with vibrant accents',
      colors: {
        bg: {
          primary: '#0a0f1b',
          secondary: 'rgba(255, 255, 255, 0.05)',
          tertiary: 'rgba(255, 255, 255, 0.1)',
          overlay: 'rgba(10, 15, 27, 0.8)',
          glass: {
            primary: 'rgba(255, 255, 255, 0.08)',
            secondary: 'rgba(255, 255, 255, 0.12)',
            tertiary: 'rgba(255, 255, 255, 0.16)',
          },
        },
        text: {
          primary: '#ffffff',
          secondary: 'rgba(255, 255, 255, 0.8)',
          tertiary: 'rgba(255, 255, 255, 0.6)',
          inverse: '#0a0f1b',
        },
        brand: {
          primary: '#00d4ff', // Cyan
          secondary: '#ff00ff', // Magenta
          accent: '#7b00ff', // Purple
        },
        semantic: {
          error: '#ff3b5c',
          warning: '#ffaa00',
          success: '#00ff88',
          info: '#00aaff',
        },
      },
      typography: {
        heading:
          '"SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
        body: '"SF Pro Text", -apple-system, BlinkMacSystemFont, sans-serif',
      },
      shadows: {
        sm: '0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 212, 255, 0.1)',
        md: '0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 212, 255, 0.15)',
        lg: '0 8px 32px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(255, 0, 255, 0.1)',
        xl: '0 16px 64px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(123, 0, 255, 0.15)',
      },
      borders: {
        radius: {
          button: '1rem',
          card: '1.5rem',
          modal: '2rem',
        },
        glass: {
          light: 'rgba(255, 255, 255, 0.2)',
          dark: 'rgba(255, 255, 255, 0.1)',
        },
      },
      effects: {
        blur: '12px',
        saturation: '1.8',
        opacity: '0.85',
        gradients: {
          primary:
            'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
          accent1:
            'radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.2) 0%, transparent 50%)',
          accent2:
            'radial-gradient(circle at 80% 50%, rgba(255, 0, 255, 0.2) 0%, transparent 50%)',
        },
      },
    },
  },

  // Component-specific theme overrides
  components: {
    button: {
      variants: {
        primary: {
          base: 'bg-brand-primary text-text-inverse',
          hover: 'hover:bg-brand-primary/90',
          active: 'active:scale-95',
          disabled: 'disabled:opacity-50 disabled:cursor-not-allowed',
        },
        secondary: {
          base: 'bg-bg-secondary text-brand-primary border border-brand-primary/20',
          hover: 'hover:border-brand-primary/40 hover:bg-bg-tertiary',
          active: 'active:scale-95',
          disabled: 'disabled:opacity-50 disabled:cursor-not-allowed',
        },
        ghost: {
          base: 'text-brand-primary',
          hover: 'hover:bg-brand-primary/10',
          active: 'active:scale-95',
          disabled: 'disabled:opacity-50 disabled:cursor-not-allowed',
        },
      },
    },
    card: {
      base: 'bg-bg-secondary rounded-theme shadow-theme-md',
      hover: 'hover:shadow-theme-lg transition-shadow duration-300',
    },
    input: {
      base: 'bg-bg-tertiary border border-brand-primary/20 text-text-primary rounded-theme',
      focus:
        'focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20',
      error: 'border-red-500 focus:border-red-500 focus:ring-red-500/20',
    },
  },

  // Animation settings
  animations: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    },
  },

  // Mobile-specific settings
  mobile: {
    touchTarget: {
      min: '44px',
      comfortable: '48px',
      large: '56px',
    },
    haptic: {
      enabled: true,
      duration: 10, // milliseconds
    },
  },
}

export type ThemeConfig = typeof themeConfig
export type ThemeName = keyof typeof themeConfig.themes
