import { describe, it, expect } from 'vitest'
import { render } from '@testing-library/react'

describe('Glassmorphism Theme', () => {
  it('should have glassmorphism CSS variables defined', () => {
    const style = document.createElement('style')
    style.textContent = `
      :root[data-theme='glassmorphism'] {
        --glass-blur: 12px;
        --glass-saturation: 1.8;
        --glass-opacity: 0.85;
        --glass-border-light: rgba(255, 255, 255, 0.2);
        --glass-border-dark: rgba(255, 255, 255, 0.1);
        --glass-gradient-1: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        --glass-gradient-2: radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.2) 0%, transparent 50%);
        --glass-gradient-3: radial-gradient(circle at 80% 50%, rgba(255, 0, 255, 0.2) 0%, transparent 50%);
      }
    `
    document.head.appendChild(style)
    document.documentElement.setAttribute('data-theme', 'glassmorphism')

    const computedStyle = getComputedStyle(document.documentElement)
    expect(computedStyle.getPropertyValue('--glass-blur')).toBeTruthy()
    expect(computedStyle.getPropertyValue('--glass-saturation')).toBeTruthy()
    expect(computedStyle.getPropertyValue('--glass-opacity')).toBeTruthy()

    document.head.removeChild(style)
  })

  it('should apply glass effect classes correctly', () => {
    const { container } = render(
      <div className="glass-primary">
        <div className="glass-secondary">
          <div className="glass-tertiary">Content</div>
        </div>
      </div>
    )

    const primaryGlass = container.querySelector('.glass-primary')
    const secondaryGlass = container.querySelector('.glass-secondary')
    const tertiaryGlass = container.querySelector('.glass-tertiary')

    expect(primaryGlass).toBeTruthy()
    expect(secondaryGlass).toBeTruthy()
    expect(tertiaryGlass).toBeTruthy()
  })

  it('should have proper backdrop filter support', () => {
    const testDiv = document.createElement('div')
    testDiv.style.backdropFilter = 'blur(10px)'

    // Check if backdrop-filter is supported
    const isSupported =
      testDiv.style.backdropFilter !== undefined ||
      testDiv.style.webkitBackdropFilter !== undefined

    expect(isSupported).toBe(true)
  })

  it('should have glassmorphism-specific color values', () => {
    const style = document.createElement('style')
    style.textContent = `
      :root[data-theme='glassmorphism'] {
        --color-bg-primary: #0a0f1b;
        --color-bg-glass-1: rgba(255, 255, 255, 0.08);
        --color-bg-glass-2: rgba(255, 255, 255, 0.12);
        --color-bg-glass-3: rgba(255, 255, 255, 0.16);
      }
    `
    document.head.appendChild(style)
    document.documentElement.setAttribute('data-theme', 'glassmorphism')

    const computedStyle = getComputedStyle(document.documentElement)
    expect(computedStyle.getPropertyValue('--color-bg-glass-1')).toBeTruthy()
    expect(computedStyle.getPropertyValue('--color-bg-glass-2')).toBeTruthy()
    expect(computedStyle.getPropertyValue('--color-bg-glass-3')).toBeTruthy()

    document.head.removeChild(style)
  })
})
