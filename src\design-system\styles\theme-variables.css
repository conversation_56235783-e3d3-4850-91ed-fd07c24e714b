/* Dr. Muscle X Design System - Theme Variables */

/* Subtle Depth Theme */
:root[data-theme='subtle-depth'],
html[data-theme='subtle-depth'],
[data-theme='subtle-depth'] {
  /* Colors */
  --color-bg-primary: #0a0a0b;
  --color-bg-secondary: #1a1a1c;
  --color-bg-tertiary: #2a2a2c;
  --color-bg-overlay: rgba(0, 0, 0, 0.7);

  --color-text-primary: #ffffff;
  --color-text-secondary: #b8b8bc;
  --color-text-tertiary: #7a7a7e;
  --color-text-inverse: #0a0a0b;

  --color-brand-primary: #d4af37;
  --color-brand-secondary: #f7e98e;
  --color-brand-accent: #fff8dc;

  /* Gradients */
  --gradient-bg-premium: linear-gradient(
    135deg,
    #0a0a0b 0%,
    #141416 50%,
    #0a0a0b 100%
  );
  --gradient-metallic-gold: linear-gradient(
    135deg,
    #d4af37 0%,
    #f7e98e 50%,
    #d4af37 100%
  );
  --gradient-metallic-silver: linear-gradient(
    135deg,
    #b8b8bc 0%,
    #ffffff 50%,
    #b8b8bc 100%
  );
  --gradient-overlay-subtle: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.1) 100%
  );
  --gradient-overlay-premium: linear-gradient(
    180deg,
    rgba(212, 175, 55, 0.05) 0%,
    rgba(0, 0, 0, 0.2) 100%
  );
  --gradient-shimmer: linear-gradient(
    105deg,
    transparent 40%,
    rgba(212, 175, 55, 0.7) 50%,
    transparent 60%
  );

  /* Typography */
  --font-heading: 'Playfair Display', serif;
  --font-body: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Letter Spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Text Shadows */
  --text-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --text-shadow-md: 0 2px 4px rgba(0, 0, 0, 0.3);
  --text-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.3);
  --text-shadow-gold: 0 2px 4px rgba(212, 175, 55, 0.3);

  /* Shadows - Enhanced with golden glow for subtle-depth */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(212, 175, 55, 0.1);
  --shadow-md:
    0 4px 12px rgba(0, 0, 0, 0.6), 0 2px 4px rgba(212, 175, 55, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.7), 0 4px 8px rgba(212, 175, 55, 0.2);
  --shadow-xl:
    0 16px 48px rgba(0, 0, 0, 0.8), 0 8px 16px rgba(212, 175, 55, 0.25);

  /* Border Radius */
  --radius-button: 0.5rem;

  /* Animation */
  --animation-duration-luxury: 300ms;
  --animation-easing-luxury: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Flat Bold Theme */
:root[data-theme='flat-bold'],
html[data-theme='flat-bold'],
[data-theme='flat-bold'] {
  /* Colors */
  --color-bg-primary: #000000;
  --color-bg-secondary: #ffffff;
  --color-bg-tertiary: #f5f5f5;
  --color-bg-overlay: rgba(0, 0, 0, 0.9);

  --color-text-primary: #ffffff;
  --color-text-secondary: #000000;
  --color-text-tertiary: #666666;
  --color-text-inverse: #000000;

  --color-brand-primary: #00ff88;
  --color-brand-secondary: #00cc6a;
  --color-brand-accent: #00ff88;

  /* Typography */
  --font-heading: 'Bebas Neue', sans-serif;
  --font-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Shadows (none for flat design) */
  --shadow-sm: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;

  /* Border Radius */
  --radius-button: 0;
}

/* Glassmorphism Theme */
:root[data-theme='glassmorphism'],
html[data-theme='glassmorphism'],
[data-theme='glassmorphism'] {
  /* Colors */
  --color-bg-primary: #0a0f1b;
  --color-bg-secondary: rgba(255, 255, 255, 0.05);
  --color-bg-tertiary: rgba(255, 255, 255, 0.1);
  --color-bg-overlay: rgba(10, 15, 27, 0.8);

  /* Glass-specific backgrounds */
  --color-bg-glass-1: rgba(255, 255, 255, 0.08);
  --color-bg-glass-2: rgba(255, 255, 255, 0.12);
  --color-bg-glass-3: rgba(255, 255, 255, 0.16);

  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-tertiary: rgba(255, 255, 255, 0.6);
  --color-text-inverse: #0a0f1b;

  --color-brand-primary: #00d4ff;
  --color-brand-secondary: #ff00ff;
  --color-brand-accent: #7b00ff;

  /* Glass Effects */
  --glass-blur: 12px;
  --glass-saturation: 1.8;
  --glass-opacity: 0.85;
  --glass-border-light: rgba(255, 255, 255, 0.2);
  --glass-border-dark: rgba(255, 255, 255, 0.1);

  /* Gradient Overlays */
  --glass-gradient-1: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  --glass-gradient-2: radial-gradient(
    circle at 20% 50%,
    rgba(0, 212, 255, 0.2) 0%,
    transparent 50%
  );
  --glass-gradient-3: radial-gradient(
    circle at 80% 50%,
    rgba(255, 0, 255, 0.2) 0%,
    transparent 50%
  );

  /* Typography */
  --font-heading:
    'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-body: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Shadows - Enhanced with colorful glow */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 212, 255, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 212, 255, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(255, 0, 255, 0.1);
  --shadow-xl:
    0 16px 64px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(123, 0, 255, 0.15);

  /* Border Radius */
  --radius-button: 1rem;
}

/* Ultra-Minimal Theme */
:root[data-theme='ultra-minimal'],
html[data-theme='ultra-minimal'],
[data-theme='ultra-minimal'] {
  /* Colors */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #fafafa;
  --color-bg-tertiary: #f5f5f5;
  --color-bg-overlay: rgba(0, 0, 0, 0.05);

  --color-text-primary: #000000;
  --color-text-secondary: #333333;
  --color-text-tertiary: #666666;
  --color-text-inverse: #ffffff;

  --color-brand-primary: #000000;
  --color-brand-secondary: #333333;
  --color-brand-accent: #0066ff;

  /* Typography */
  --font-heading: 'Didot', Georgia, serif;
  --font-body: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, sans-serif;

  /* Shadows (none for minimal design) */
  --shadow-sm: none;
  --shadow-md: none;
  --shadow-lg: none;
  --shadow-xl: none;

  /* Border Radius */
  --radius-button: 0;
}

/* Default to subtle-depth if no theme is set */
:root {
  /* Copy subtle-depth theme as default */
  --color-bg-primary: #0a0a0b;
  --color-bg-secondary: #1a1a1c;
  --color-bg-tertiary: #2a2a2c;
  --color-bg-overlay: rgba(0, 0, 0, 0.7);

  --color-text-primary: #ffffff;
  --color-text-secondary: #b8b8bc;
  --color-text-tertiary: #7a7a7e;
  --color-text-inverse: #0a0a0b;

  --color-brand-primary: #d4af37;
  --color-brand-secondary: #f7e98e;
  --color-brand-accent: #fff8dc;

  --font-heading: 'Playfair Display', serif;
  --font-body: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(212, 175, 55, 0.1);
  --shadow-md:
    0 4px 12px rgba(0, 0, 0, 0.6), 0 2px 4px rgba(212, 175, 55, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.7), 0 4px 8px rgba(212, 175, 55, 0.2);
  --shadow-xl:
    0 16px 48px rgba(0, 0, 0, 0.8), 0 8px 16px rgba(212, 175, 55, 0.25);

  --radius-button: 0.5rem;
}
