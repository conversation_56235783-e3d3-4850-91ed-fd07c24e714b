import React from 'react'
import { render, screen, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ThemeProvider, useTheme } from '../context'

// Test component that uses the theme
function TestComponent() {
  const { theme, setTheme } = useTheme()
  return (
    <div>
      <span data-testid="current-theme">{theme}</span>
      <button onClick={() => setTheme('flat-bold')}>Set Flat Bold</button>
      <button onClick={() => setTheme('glassmorphism')}>
        Set Glassmorphism
      </button>
    </div>
  )
}

describe('ThemeProvider', () => {
  beforeEach(() => {
    // Clear localStorage
    localStorage.clear()
    // Clear any data attributes
    document.documentElement.removeAttribute('data-theme')
  })

  it('should set data-theme attribute on document element when theme changes', async () => {
    const user = userEvent.setup()

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    )

    // Check initial theme
    expect(screen.getByTestId('current-theme')).toHaveTextContent(
      'subtle-depth'
    )
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'subtle-depth'
    )

    // Change theme
    await user.click(screen.getByText('Set Flat Bold'))

    // Check that data-theme attribute was updated
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'flat-bold'
    )
    expect(screen.getByTestId('current-theme')).toHaveTextContent('flat-bold')

    // Change theme again
    await user.click(screen.getByText('Set Glassmorphism'))

    // Check that data-theme attribute was updated again
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'glassmorphism'
    )
    expect(screen.getByTestId('current-theme')).toHaveTextContent(
      'glassmorphism'
    )
  })

  it('should persist theme to localStorage', async () => {
    const user = userEvent.setup()

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    )

    // Wait for component to mount
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0))
    })

    await user.click(screen.getByText('Set Flat Bold'))

    // Wait for effect to run
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0))
    })

    expect(localStorage.getItem('dr-muscle-x-theme')).toBe('flat-bold')
  })

  it('should load theme from localStorage on mount', () => {
    localStorage.setItem('dr-muscle-x-theme', 'glassmorphism')

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    )

    expect(screen.getByTestId('current-theme')).toHaveTextContent(
      'glassmorphism'
    )
    expect(document.documentElement.getAttribute('data-theme')).toBe(
      'glassmorphism'
    )
  })
})
