'use client'

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useMemo,
} from 'react'
import { ThemeVariant } from '../types'

interface ThemeContextValue {
  theme: ThemeVariant
  setTheme: (theme: ThemeVariant) => void
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined)

const THEME_STORAGE_KEY = 'dr-muscle-x-theme'
const VALID_THEMES: ThemeVariant[] = [
  'subtle-depth',
  'flat-bold',
  'glassmorphism',
  'ultra-minimal',
]

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [theme, setThemeState] = useState<ThemeVariant>('subtle-depth')
  const [mounted, setMounted] = useState(false)

  // Load theme from localStorage on mount
  useEffect(() => {
    setMounted(true)
    try {
      const savedTheme = localStorage.getItem(THEME_STORAGE_KEY)
      if (savedTheme && VALID_THEMES.includes(savedTheme as ThemeVariant)) {
        setThemeState(savedTheme as ThemeVariant)
      }
    } catch (error) {
      console.warn('Failed to load theme from localStorage:', error)
    }
  }, [])

  // Update CSS variables and localStorage when theme changes
  useEffect(() => {
    if (!mounted) return

    // Update data attribute for CSS
    document.documentElement.setAttribute('data-theme', theme)

    // Persist to localStorage
    try {
      if (typeof localStorage !== 'undefined' && localStorage.setItem) {
        localStorage.setItem(THEME_STORAGE_KEY, theme)
      }
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }, [theme, mounted])

  const setTheme = (newTheme: ThemeVariant) => {
    if (VALID_THEMES.includes(newTheme)) {
      setThemeState(newTheme)
    } else {
      console.warn(`Invalid theme variant: ${newTheme}`)
    }
  }

  const contextValue = useMemo(() => ({ theme, setTheme }), [theme])

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
