/**
 * Subtle Depth Theme Tokens
 * Premium sophistication with rich, layered interfaces
 */

import { Theme } from '../types'

export const subtleDepthTheme: Theme = {
  name: 'subtle-depth',
  gradients: {
    backgroundPremium:
      'linear-gradient(135deg, #0A0A0B 0%, #141416 50%, #0A0A0B 100%)',
    metallicGold:
      'linear-gradient(135deg, #D4AF37 0%, #F7E98E 50%, #D4AF37 100%)',
    metallicSilver:
      'linear-gradient(135deg, #B8B8BC 0%, #FFFFFF 50%, #B8B8BC 100%)',
    overlaySubtle:
      'linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 100%)',
    overlayPremium:
      'linear-gradient(180deg, rgba(212, 175, 55, 0.05) 0%, rgba(0, 0, 0, 0.2) 100%)',
    shimmer:
      'linear-gradient(105deg, transparent 40%, rgba(212, 175, 55, 0.7) 50%, transparent 60%)',
  },
  colors: {
    background: {
      primary: '#0A0A0B',
      secondary: '#1A1A1C',
      tertiary: '#2A2A2C',
      overlay: 'rgba(0, 0, 0, 0.7)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#B8B8BC',
      tertiary: '#7A7A7E',
      inverse: '#0A0A0B',
    },
    brand: {
      primary: '#D4AF37', // Luxury gold
      secondary: '#F7E98E',
      accent: '#FFF8DC',
    },
    status: {
      success: '#4ADE80',
      error: '#EF4444',
      warning: '#F59E0B',
      info: '#3B82F6',
    },
    interactive: {
      hover: 'rgba(212, 175, 55, 0.1)',
      active: 'rgba(212, 175, 55, 0.2)',
      disabled: 'rgba(255, 255, 255, 0.1)',
    },
  },
  typography: {
    fonts: {
      heading: '"Playfair Display", serif',
      body: '"SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
      mono: '"SF Mono", monospace',
    },
    sizes: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '2rem',
      '4xl': '3rem',
    },
    weights: {
      light: 300,
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700,
    },
    lineHeights: {
      tight: 1.2,
      normal: 1.5,
      relaxed: 1.75,
    },
    letterSpacing: {
      tight: '-0.025em',
      normal: '0',
      wide: '0.025em',
      wider: '0.05em',
      widest: '0.1em',
    },
    textShadows: {
      sm: '0 1px 2px rgba(0, 0, 0, 0.3)',
      md: '0 2px 4px rgba(0, 0, 0, 0.3)',
      lg: '0 4px 8px rgba(0, 0, 0, 0.3)',
      gold: '0 2px 4px rgba(212, 175, 55, 0.3)',
    },
  },
  spacing: {
    unit: 4,
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
    '4xl': '6rem',
  },
  shadows: {
    none: 'none',
    sm: '0 1px 3px rgba(0, 0, 0, 0.3)',
    md: '0 4px 6px rgba(0, 0, 0, 0.3)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.3)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.3)',
    inner: 'inset 0 2px 4px rgba(0, 0, 0, 0.3)',
  },
  animations: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
      spring: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
      luxury: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
    keyframes: {
      shimmer: 'shimmer 2s linear infinite',
    },
  },
  borders: {
    radius: {
      none: '0',
      sm: '0.25rem',
      md: '0.5rem',
      lg: '1rem',
      full: '9999px',
    },
    width: {
      hairline: '0.5px',
      thin: '1px',
      medium: '2px',
      thick: '4px',
    },
  },
}
