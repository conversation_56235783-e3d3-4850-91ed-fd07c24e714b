import { renderHook, act, waitFor } from '@testing-library/react'
import { vi } from 'vitest'
import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useWorkoutActions } from '../useWorkoutActions'
import { useWorkoutStore } from '@/stores/workoutStore'
import type { WorkoutTemplateGroupModel } from '@/types'

// Mock dependencies
vi.mock('@/stores/workoutStore')
vi.mock('@/utils/workoutCache')
vi.mock('@/utils/logger')

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  // eslint-disable-next-line react/function-component-definition
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

describe('useWorkoutActions - Recommendation Loading', () => {
  const mockSetWorkout = vi.fn()
  const mockStartWorkoutStore = vi.fn()
  const mockSetError = vi.fn()
  const mockLoadAllExerciseRecommendations = vi.fn()

  const mockWorkoutGroup: WorkoutTemplateGroupModel[] = [
    {
      Id: 1,
      Label: 'Push Day',
      WorkoutTemplates: [
        {
          Id: 100,
          Label: 'Chest & Triceps',
          IsSystemExercise: true,
          UserId: '',
          Exercises: [
            {
              Id: 1001,
              Label: 'Bench Press',
              IsBodyweight: false,
              IsEasy: false,
              IsSystemExercise: true,
              IsSwappable: true,
              IsTimeBased: false,
              IsUnilateral: false,
              VideoUrl: '',
              EquipmentModel: { Id: 1, Label: 'Barbell' },
              LastTimeModifiedBy: new Date(),
              Minutes: 2,
              Reps: 8,
              Sets: 3,
              SetsModel: {
                Series: '3',
                Reps: '8',
                SetStyle: 'Normal',
              },
              BodyPartId: 1,
              SelectedEquipmentModelId: 1,
              UnilateralOption: 0,
              SelectedUnilateralOption: 0,
            },
            {
              Id: 1002,
              Label: 'Shoulder Press',
              IsBodyweight: false,
              IsEasy: false,
              IsSystemExercise: true,
              IsSwappable: true,
              IsTimeBased: false,
              IsUnilateral: false,
              VideoUrl: '',
              EquipmentModel: { Id: 2, Label: 'Dumbbell' },
              LastTimeModifiedBy: new Date(),
              Minutes: 2,
              Reps: 10,
              Sets: 3,
              SetsModel: {
                Series: '3',
                Reps: '10',
                SetStyle: 'Normal',
              },
              BodyPartId: 2,
              SelectedEquipmentModelId: 2,
              UnilateralOption: 0,
              SelectedUnilateralOption: 0,
            },
          ],
          WorkoutSettingsModel: {},
        },
      ],
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useWorkoutStore).mockReturnValue({
      setWorkout: mockSetWorkout,
      startWorkout: mockStartWorkoutStore,
      setError: mockSetError,
      loadAllExerciseRecommendations: mockLoadAllExerciseRecommendations,
      workoutSession: null,
    } as any)
  })

  it('should load all exercise recommendations after starting workout', async () => {
    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      const success = await result.current.startWorkout(mockWorkoutGroup)
      expect(success).toBe(true)
    })

    // Verify workout was set up
    expect(mockStartWorkoutStore).toHaveBeenCalledTimes(1)
    expect(mockSetWorkout).toHaveBeenCalledWith(
      mockWorkoutGroup[0].WorkoutTemplates[0]
    )

    // Verify recommendations were loaded
    await waitFor(() => {
      expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
    })
  })

  it('should handle errors when loading recommendations fails', async () => {
    mockLoadAllExerciseRecommendations.mockRejectedValueOnce(
      new Error('Failed to load recommendations')
    )

    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      const success = await result.current.startWorkout(mockWorkoutGroup)
      // Workout should still start successfully
      expect(success).toBe(true)
    })

    // Verify workout was set up
    expect(mockStartWorkoutStore).toHaveBeenCalledTimes(1)
    expect(mockSetWorkout).toHaveBeenCalledWith(
      mockWorkoutGroup[0].WorkoutTemplates[0]
    )

    // Verify recommendations loading was attempted
    await waitFor(() => {
      expect(mockLoadAllExerciseRecommendations).toHaveBeenCalledTimes(1)
    })
  })

  it('should not load recommendations if workout data is invalid', async () => {
    const { result } = renderHook(() => useWorkoutActions(), {
      wrapper: createWrapper(),
    })

    await act(async () => {
      const success = await result.current.startWorkout([])
      expect(success).toBe(false)
    })

    expect(mockLoadAllExerciseRecommendations).not.toHaveBeenCalled()
    expect(mockSetError).toHaveBeenCalledWith('Invalid workout data')
  })
})
