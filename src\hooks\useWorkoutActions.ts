import { useCallback } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useWorkoutStore } from '@/stores/workoutStore/index'
import { WorkoutCache } from '@/utils/workoutCache'
import { logger } from '@/utils/logger'
import type { WorkoutTemplateGroupModel } from '@/types'

export function useWorkoutActions() {
  const queryClient = useQueryClient()
  const {
    setWorkout,
    startWorkout: startWorkoutStore,
    nextSet: nextSetStore,
    nextExercise: nextExerciseStore,
    completeWorkout: completeWorkoutStore,
    setError,
    workoutSession,
    loadAllExerciseRecommendations,
  } = useWorkoutStore()

  const startWorkout = useCallback(
    async (workout: WorkoutTemplateGroupModel[]) => {
      try {
        logger.log('Starting workout')

        // Extract the first workout template from the group
        if (
          !workout ||
          workout.length === 0 ||
          !workout[0]?.WorkoutTemplates?.length
        ) {
          throw new Error('Invalid workout data')
        }

        const firstGroup = workout[0]
        const workoutTemplate = firstGroup.WorkoutTemplates[0]

        if (!workoutTemplate) {
          throw new Error('No workout template found')
        }

        startWorkoutStore()
        setWorkout(workoutTemplate)

        // Cache the workout
        WorkoutCache.set(workout)

        // Load all exercise recommendations in parallel
        // This follows the mobile app pattern to ensure smooth exercise loading
        logger.log('Loading exercise recommendations...')
        try {
          await loadAllExerciseRecommendations()
          logger.log('Exercise recommendations loaded successfully')
        } catch (error) {
          // Log error but don't fail workout start
          logger.error('Failed to load exercise recommendations:', error)
        }

        return true
      } catch (error) {
        logger.error('Failed to start workout:', error)
        setError(
          error instanceof Error ? error.message : 'Failed to start workout'
        )
        return false
      }
    },
    [startWorkoutStore, setWorkout, setError, loadAllExerciseRecommendations]
  )

  const nextSet = useCallback(() => {
    try {
      nextSetStore()
    } catch (error) {
      logger.error('Failed to move to next set:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to move to next set'
      )
    }
  }, [nextSetStore, setError])

  const nextExercise = useCallback(() => {
    try {
      nextExerciseStore()
    } catch (error) {
      logger.error('Failed to move to next exercise:', error)
      setError(
        error instanceof Error
          ? error.message
          : 'Failed to move to next exercise'
      )
    }
  }, [nextExerciseStore, setError])

  const completeWorkoutMutation = useMutation({
    mutationFn: async () => {
      if (!workoutSession) {
        throw new Error('No active workout session')
      }

      // Complete the workout locally first
      completeWorkoutStore()

      // Clear the workout cache after completion
      WorkoutCache.clear()

      // TODO: Sync any pending sets before completing
      // const pendingSets = SyncManager.getPendingSets(workoutSession.id)
      // if (pendingSets.length > 0) {
      //   logger.log(`Syncing ${pendingSets.length} pending sets before completion`)
      //   // Note: In production, this should be handled by the sync service
      // }

      // Mark workout as completed on the backend
      // TODO: Fix API call signature
      return true // await workoutApi.completeWorkout(workoutSession.id)
    },
    onSuccess: () => {
      logger.log('Workout completed successfully')
      queryClient.invalidateQueries({ queryKey: ['userWorkouts'] })
      queryClient.invalidateQueries({ queryKey: ['todaysWorkout'] })
    },
    onError: (error) => {
      logger.error('Failed to complete workout:', error)
      setError(
        error instanceof Error ? error.message : 'Failed to complete workout'
      )
    },
  })

  const completeWorkout = useCallback(async () => {
    return completeWorkoutMutation.mutateAsync()
  }, [completeWorkoutMutation])

  return {
    startWorkout,
    nextSet,
    nextExercise,
    completeWorkout,
    isCompletingWorkout: completeWorkoutMutation.isPending,
  }
}
