import { useEffect, useCallback, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { workoutApi } from '@/api/workouts'
import { useWorkoutStore } from '@/stores/workoutStore/index'
import { useAuthStore } from '@/stores/authStore'
import { ExerciseLoadingStateManager } from '@/utils/exerciseLoadingState'
import { SetLoader } from '@/api/setLoader'
import { logger } from '@/utils/logger'
import { getUserWorkoutProgramInfo } from '@/services/api/workout'
import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  ExerciseModel,
  ExerciseWorkSetsModel,
} from '@/types'
import type { GetUserWorkoutProgramTimeZoneInfoResponse } from '@/services/api/workout'
import {
  updateExerciseSets,
  markExerciseLoading,
  markExerciseError,
} from '@/types/workout'

// Create singleton instances for progressive loading
const exerciseLoadingStateManager = new ExerciseLoadingStateManager()
const setLoader = new SetLoader()

// Helper to extract exercises from workout template groups
export function extractExercisesFromWorkoutGroups(
  workoutGroups: WorkoutTemplateGroupModel[] | null
): ExerciseModel[] {
  logger.log('[extractExercisesFromWorkoutGroups] Input:', {
    hasGroups: !!workoutGroups,
    groupsLength: workoutGroups?.length || 0,
    firstGroup: workoutGroups?.[0] ? 'exists' : 'null',
  })

  if (!workoutGroups || workoutGroups.length === 0) {
    logger.log('[extractExercisesFromWorkoutGroups] No groups, returning []')
    return []
  }

  const firstGroup = workoutGroups[0]
  logger.log('[extractExercisesFromWorkoutGroups] First group:', {
    hasWorkoutTemplates: !!firstGroup?.WorkoutTemplates,
    templatesLength: firstGroup?.WorkoutTemplates?.length || 0,
  })

  if (
    !firstGroup ||
    !firstGroup.WorkoutTemplates ||
    firstGroup.WorkoutTemplates.length === 0
  ) {
    logger.log('[extractExercisesFromWorkoutGroups] No templates, returning []')
    return []
  }

  const firstTemplate = firstGroup.WorkoutTemplates[0]
  // Check both 'Exercises' (English) and 'Exercices' (French) field names
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const exercises =
    firstTemplate?.Exercises || (firstTemplate as any)?.Exercices || []

  logger.log('[extractExercisesFromWorkoutGroups] Result:', {
    hasExercises: !!firstTemplate?.Exercises,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    hasExercices: !!(firstTemplate as any)?.Exercices,
    exerciseCount: exercises.length,
    firstExercise: exercises[0]?.Label || 'none',
  })

  return exercises
}

export function useWorkoutDataLoader() {
  const { isAuthenticated } = useAuthStore()
  const {
    setCachedUserProgramInfo,
    setCachedUserWorkouts,
    setCachedTodaysWorkout,
    hasHydrated,
  } = useWorkoutStore()

  // Optimistic data loading state
  const [optimisticData, setOptimisticData] = useState<{
    userProgramInfo: GetUserWorkoutProgramTimeZoneInfoResponse | null
    userWorkouts: WorkoutTemplateModel[] | null
    todaysWorkout: WorkoutTemplateGroupModel[] | null
    exercises: ExerciseModel[] | null
  }>({
    userProgramInfo: null,
    userWorkouts: null,
    todaysWorkout: null,
    exercises: null,
  })

  // User program info query - always enabled when authenticated and hydrated
  const userProgramInfoQuery = useQuery({
    queryKey: ['userProgramInfo'],
    queryFn: getUserWorkoutProgramInfo,
    enabled: isAuthenticated && hasHydrated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // User workouts query - always enabled when authenticated and hydrated
  const userWorkoutsQuery = useQuery({
    queryKey: ['userWorkouts'],
    queryFn: () => workoutApi.getUserWorkout(),
    enabled: isAuthenticated && hasHydrated,
    staleTime: 5 * 60 * 1000,
  })

  // Today's workout query - always enabled when authenticated and hydrated
  const todaysWorkoutQuery = useQuery({
    queryKey: ['todaysWorkout'],
    queryFn: () => workoutApi.getTodaysWorkout(),
    enabled: isAuthenticated && hasHydrated,
    staleTime: 5 * 60 * 1000,
  })

  // Update caches when data is fetched
  useEffect(() => {
    if (userProgramInfoQuery.data) {
      setCachedUserProgramInfo(userProgramInfoQuery.data)
      setOptimisticData((prev) => ({
        ...prev,
        userProgramInfo: userProgramInfoQuery.data,
      }))
    }
  }, [userProgramInfoQuery.data, setCachedUserProgramInfo])

  useEffect(() => {
    if (userWorkoutsQuery.data) {
      setCachedUserWorkouts(userWorkoutsQuery.data)
      setOptimisticData((prev) => ({
        ...prev,
        userWorkouts: userWorkoutsQuery.data,
      }))
    }
  }, [userWorkoutsQuery.data, setCachedUserWorkouts])

  useEffect(() => {
    if (todaysWorkoutQuery.data) {
      setCachedTodaysWorkout(todaysWorkoutQuery.data)
      const exercises = extractExercisesFromWorkoutGroups(
        todaysWorkoutQuery.data
      )
      setOptimisticData((prev) => ({
        ...prev,
        todaysWorkout: todaysWorkoutQuery.data,
        exercises,
      }))
    }
  }, [todaysWorkoutQuery.data, setCachedTodaysWorkout])

  const loadExerciseSets = useCallback(
    async (
      exerciseIds: number[],
      _exerciseWorkSetsModels: ExerciseWorkSetsModel[],
      setExerciseWorkSetsModels: React.Dispatch<
        React.SetStateAction<ExerciseWorkSetsModel[]>
      >
    ) => {
      if (exerciseIds.length === 0) return

      // Mark exercises as loading
      exerciseIds.forEach((id) => {
        exerciseLoadingStateManager.startLoading(id)
      })

      // Update UI to show loading state
      setExerciseWorkSetsModels((prev: ExerciseWorkSetsModel[]) =>
        prev.map((exercise: ExerciseWorkSetsModel) => {
          if (exerciseIds.includes(exercise.Id)) {
            return markExerciseLoading(exercise)
          }
          return exercise
        })
      )

      // Load sets in parallel for all exercises
      const results = await setLoader.batchLoadExerciseSets(exerciseIds)

      // Process results and update state
      setExerciseWorkSetsModels((prev: ExerciseWorkSetsModel[]) =>
        prev.map((exercise: ExerciseWorkSetsModel) => {
          const result = results[exercise.Id]
          if (!result) return exercise

          if (result.success) {
            exerciseLoadingStateManager.completeLoading(exercise.Id)
            return updateExerciseSets(exercise, result.sets)
          } else {
            exerciseLoadingStateManager.failLoading(
              exercise.Id,
              result.error || 'Failed to load sets'
            )
            return markExerciseError(
              exercise,
              result.error || 'Failed to load sets'
            )
          }
        })
      )
    },
    []
  )

  const refetchAll = useCallback(() => {
    return Promise.all([
      userProgramInfoQuery.refetch(),
      userWorkoutsQuery.refetch(),
      todaysWorkoutQuery.refetch(),
    ])
  }, [userProgramInfoQuery, userWorkoutsQuery, todaysWorkoutQuery])

  return {
    optimisticData,
    userProgramInfoQuery,
    userWorkoutsQuery,
    todaysWorkoutQuery,
    loadExerciseSets,
    refetchAll,
    isLoading:
      userProgramInfoQuery.isLoading ||
      userWorkoutsQuery.isLoading ||
      todaysWorkoutQuery.isLoading,
  }
}
