import { useCallback } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore/index'
// import { workoutApi } from '@/api/workouts'
// import { SyncManager } from '@/utils/syncManager'
import { logger } from '@/utils/logger'
import type { WorkoutLogSerieModel, MultiUnityWeight } from '@/types'

export function useWorkoutSync() {
  const { workoutSession, saveSet: saveSetStore } = useWorkoutStore()

  const saveSet = useCallback(
    async (params: {
      exerciseId: number
      weight?: number
      reps?: number
      isWarmup?: boolean
      setNumber?: number
      duration?: number
      RIR?: number
    }) => {
      const currentSession = workoutSession

      if (!currentSession || !currentSession.id) {
        logger.error('Cannot save set: No active workout session')
        throw new Error('No active workout session')
      }

      logger.log('Saving set:', {
        ...params,
        sessionId: currentSession.id,
      })

      const multiUnityWeight: MultiUnityWeight | null = params.weight
        ? {
            Lb: params.weight,
            Kg: params.weight * 0.453592,
          }
        : null

      const setData: WorkoutLogSerieModel = {
        ExerciseId: params.exerciseId,
        Weight: multiUnityWeight || { Lb: 0, Kg: 0 },
        Reps: params.reps || 0,
        IsWarmups: params.isWarmup || false,
        IsNext: false,
        IsFinished: false,
        RIR: params.RIR,
      }

      try {
        // Save locally first (optimistic update)
        saveSetStore(setData)

        // Then sync to backend
        // TODO: Implement saveSet API method
        const result = true // await workoutApi.saveSet(currentSession.id, exerciseId, setData)

        // If sync successful, mark as synced
        if (result) {
          // TODO: Implement sync tracking
          // SyncManager.markAsSynced(currentSession.id, exerciseId)
        }

        return result
      } catch (error) {
        logger.error('Failed to save set:', error)
        // Optimistic update already done, queue for retry
        // TODO: Implement offline queue
        // SyncManager.queueForSync(currentSession.id, exerciseId, setData)
        throw error
      }
    },
    [workoutSession, saveSetStore]
  )

  const syncPendingSets = useCallback(async () => {
    if (!workoutSession?.id) return

    // TODO: Implement offline sync queue
    logger.log('Sync pending sets - not implemented yet')
  }, [workoutSession])

  return {
    saveSet,
    syncPendingSets,
  }
}
