import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import {
  getUserWorkoutProgramInfo,
  getWorkoutDetails,
  getExerciseRecommendation,
  generateRecommendationCacheKey,
} from '../workout'
import { apiClient } from '@/api/client'
import { getCurrentUserEmail } from '@/lib/auth-utils'

// Mock the API client
vi.mock('@/api/client', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
  },
}))

// Mock auth utils
vi.mock('@/lib/auth-utils', () => ({
  getCurrentUserEmail: vi.fn(),
}))

// Mock Intl.DateTimeFormat
const mockTimeZone = 'America/New_York'
global.Intl.DateTimeFormat = vi.fn(() => ({
  resolvedOptions: () => ({ timeZone: mockTimeZone }),
})) as any

describe('Workout API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getUserWorkoutProgramInfo', () => {
    it('should fetch user workout program info with timezone', async () => {
      const mockResponse = {
        data: {
          GetUserProgramInfoResponseModel: {
            NextWorkoutTemplate: {
              Id: 12345,
              Label: 'Push Day',
              IsSystemExercise: true,
              Exercises: null,
            },
            RecommendedProgram: {
              Id: 100,
              Label: 'Beginner Program',
            },
          },
          LastWorkoutDate: '2025-01-30T10:00:00Z',
          LastConsecutiveWorkoutDays: 5,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
        {
          TimeZoneId: mockTimeZone,
          Offset: new Date().getTimezoneOffset() / -60,
          IsDaylightSaving: false,
        }
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle wrapped API response', async () => {
      const mockData = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      const wrappedResponse = {
        data: {
          StatusCode: 200,
          Result: mockData,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(wrappedResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockData)
    })

    it('should handle root-level data with StatusCode', async () => {
      const mockData = {
        StatusCode: 200,
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
        LastWorkoutDate: '2025-01-30T10:00:00Z',
        LastConsecutiveWorkoutDays: 5,
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce({ data: mockData })

      const result = await getUserWorkoutProgramInfo()

      // Should return data without StatusCode
      expect(result).toEqual({
        GetUserProgramInfoResponseModel:
          mockData.GetUserProgramInfoResponseModel,
        LastWorkoutDate: mockData.LastWorkoutDate,
        LastConsecutiveWorkoutDays: mockData.LastConsecutiveWorkoutDays,
      })
    })

    it('should handle Data wrapper response', async () => {
      const mockData = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
          },
        },
      }

      const wrappedResponse = {
        data: {
          StatusCode: 200,
          Data: mockData,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(wrappedResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockData)
    })

    it('should throw error on API failure', async () => {
      const mockError = new Error('Network error')
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      await expect(getUserWorkoutProgramInfo()).rejects.toThrow('Network error')
    })

    it('should return Result even when API returns hasData: false', async () => {
      const mockResult = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      const mockResponse = {
        data: {
          statusCode: 200,
          hasResult: true,
          hasData: false,
          dataKeys: ['GetUserProgramInfoResponseModel', 'StatusCode', 'Result'],
          StatusCode: 200,
          Result: mockResult,
          ErrorMessage: null,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockResult)
    })

    it('should handle camelCase API response properties', async () => {
      const mockResult = {
        GetUserProgramInfoResponseModel: {
          NextWorkoutTemplate: {
            Id: 12345,
            Label: 'Push Day',
            IsSystemExercise: true,
            Exercises: null,
          },
        },
      }

      // This matches the exact console output structure
      const mockResponse = {
        data: {
          statusCode: 200,
          hasResult: true,
          hasData: false,
          result: mockResult, // camelCase result
          errorMessage: null,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toEqual(mockResult)
    })

    it('should return null when API returns StatusCode 200 with no data', async () => {
      const mockResponse = {
        data: {
          StatusCode: 200,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getUserWorkoutProgramInfo()
      expect(result).toBeNull()
    })
  })

  describe('getWorkoutDetails', () => {
    it('should fetch workout details by ID', async () => {
      const workoutId = 12345
      const mockResponse = {
        data: {
          Id: workoutId,
          Label: 'Push Day',
          Exercises: [
            {
              Id: 1001,
              Label: 'Bench Press',
              BodyPartId: 2,
              EquipmentId: 1,
              IsBodyweight: false,
              VideoUrl: 'https://example.com/video',
              RecoModel: null,
            },
          ],
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getWorkoutDetails(workoutId)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Workout/GetUserCustomizedCurrentWorkout',
        workoutId
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle wrapped API response for workout details', async () => {
      const workoutId = 12345
      const mockData = {
        Id: workoutId,
        Label: 'Push Day',
        Exercises: [],
      }

      const wrappedResponse = {
        data: {
          StatusCode: 200,
          Result: mockData,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(wrappedResponse)

      const result = await getWorkoutDetails(workoutId)
      expect(result).toEqual(mockData)
    })

    it('should throw error on workout details failure', async () => {
      const mockError = new Error('Not found')
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      await expect(getWorkoutDetails(123)).rejects.toThrow('Not found')
    })
  })

  describe('getExerciseRecommendation', () => {
    it('should fetch exercise recommendation', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 1001,
        WorkoutId: 12345,
        IsQuickMode: false,
        LightSessionDays: null,
        IsStrengthPhase: false,
        SetStyle: 'Normal',
      }

      const mockResponse = {
        data: {
          Weight: { Kg: 80, Lb: 176 },
          Reps: 8,
          Sets: 3,
          WarmUpSets: [{ Weight: { Kg: 40, Lb: 88 }, Reps: 10 }],
          IsDeload: false,
          FirstWorkSetWeight: { Kg: 80, Lb: 176 },
          FirstWorkSetReps: 8,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      const result = await getExerciseRecommendation(request)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        request
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should get username from auth state if not provided', async () => {
      // Mock getCurrentUserEmail to return the email
      vi.mocked(getCurrentUserEmail).mockReturnValue('<EMAIL>')

      const request = {
        Username: '', // Empty username
        ExerciseId: 1001,
        WorkoutId: 12345,
        SetStyle: 'Normal',
      }

      const mockResponse = {
        data: {
          Weight: { Kg: 80, Lb: 176 },
          Reps: 8,
          Sets: 3,
        },
      }

      vi.mocked(apiClient.post).mockResolvedValueOnce(mockResponse)

      await getExerciseRecommendation(request)

      expect(apiClient.post).toHaveBeenCalledWith(
        '/api/Exercise/GetRecommendationNormalRIRForExerciseWithoutWarmupsNew',
        expect.objectContaining({
          Username: '<EMAIL>',
        })
      )
    })

    it('should return null on recommendation failure', async () => {
      const request = {
        Username: '<EMAIL>',
        ExerciseId: 1001,
        WorkoutId: 12345,
      }

      const mockError = new Error('Server error')
      vi.mocked(apiClient.post).mockRejectedValueOnce(mockError)

      const result = await getExerciseRecommendation(request)
      expect(result).toBeNull()
    })

    it.skip('should throw error if no username available', async () => {
      const request = {
        Username: '',
        ExerciseId: 1001,
        WorkoutId: 12345,
      }

      // No auth state in localStorage
      localStorage.clear()

      await expect(getExerciseRecommendation(request)).rejects.toThrow(
        'Username is required for exercise recommendations'
      )
    })
  })

  describe('generateRecommendationCacheKey', () => {
    it('should generate correct cache key', () => {
      const key = generateRecommendationCacheKey('user123', 456, 789)
      expect(key).toBe('user123-456-789')
    })
  })
})
