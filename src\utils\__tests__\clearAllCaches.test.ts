import { describe, it, expect, beforeEach, vi } from 'vitest'
import { clearAllCaches, clearAuthCaches } from '../clearAllCaches'

// Create a real localStorage mock for these tests
const createLocalStorageMock = () => {
  let store: Record<string, string> = {}
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
    get length() {
      return Object.keys(store).length
    },
    key: vi.fn((index: number) => {
      const keys = Object.keys(store)
      return keys[index] || null
    }),
  }
}

// Create sessionStorage mock
const createSessionStorageMock = () => {
  let store: Record<string, string> = {}
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key]
    }),
    clear: vi.fn(() => {
      store = {}
    }),
    get length() {
      return Object.keys(store).length
    },
    key: vi.fn((index: number) => {
      const keys = Object.keys(store)
      return keys[index] || null
    }),
  }
}

describe('clearAllCaches', () => {
  let localStorageMock: ReturnType<typeof createLocalStorageMock>
  let sessionStorageMock: ReturnType<typeof createSessionStorageMock>

  beforeEach(() => {
    // Create fresh mocks for each test
    localStorageMock = createLocalStorageMock()
    sessionStorageMock = createSessionStorageMock()

    // Replace global localStorage and sessionStorage with our mocks
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      configurable: true,
    })
    Object.defineProperty(window, 'sessionStorage', {
      value: sessionStorageMock,
      configurable: true,
    })

    vi.clearAllMocks()
  })

  describe('clearAuthCaches', () => {
    it('should clear all auth-related localStorage keys including user-stats-storage', async () => {
      // Arrange - Set up localStorage with all expected keys
      const authRelatedKeys = [
        'drmuscle-auth',
        'drmuscle-program',
        'drmuscle-cache',
        'drmuscle-failed-requests',
        'drmuscle-offline-queue',
        'drmuscle-workout',
        'user-stats-storage', // This should be cleared to prevent stats loading twice
      ]

      // Add some test data to localStorage
      authRelatedKeys.forEach((key) => {
        localStorage.setItem(key, JSON.stringify({ test: 'data' }))
      })

      // Also add a non-auth key that should NOT be cleared
      localStorage.setItem('other-key', 'should-remain')

      // Act
      await clearAuthCaches()

      // Assert - All auth keys should be cleared
      authRelatedKeys.forEach((key) => {
        expect(localStorage.getItem(key)).toBeNull()
      })

      // Non-auth key should remain
      expect(localStorage.getItem('other-key')).toBe('should-remain')
    })

    it('should handle errors when clearing localStorage', async () => {
      // Arrange - Mock localStorage.removeItem to throw an error
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      // Mock the removeItem function to throw an error
      const originalRemoveItem = localStorageMock.removeItem
      localStorageMock.removeItem = vi
        .fn()
        .mockImplementationOnce(() => {
          throw new Error('Storage error')
        })
        .mockImplementation(originalRemoveItem)

      localStorage.setItem('drmuscle-auth', 'test')

      // Act
      await clearAuthCaches()

      // Assert - Should log error but not throw
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Failed to clear drmuscle-auth:',
        expect.any(Error)
      )

      // Cleanup
      consoleErrorSpy.mockRestore()
    })

    it('should clear sessionStorage', async () => {
      // Arrange
      sessionStorage.setItem('test-key', 'test-value')

      // Act
      await clearAuthCaches()

      // Assert
      expect(sessionStorage.length).toBe(0)
    })
  })

  describe('clearAllCaches', () => {
    it('should clear all localStorage keys used by stores', async () => {
      // Arrange - Set up localStorage with all expected keys
      const storesToClear = [
        'drmuscle-auth',
        'drmuscle-workout',
        'drmuscle-program',
        'drmuscle-cache',
        'drmuscle-failed-requests',
        'drmuscle-offline-queue',
      ]

      // Add some test data to localStorage
      storesToClear.forEach((key) => {
        localStorage.setItem(key, JSON.stringify({ test: 'data' }))
      })

      // Also add user-stats-storage which should be cleared in clearAuthCaches but not clearAllCaches
      localStorage.setItem('user-stats-storage', 'stats-data')

      // Act
      await clearAllCaches()

      // Assert - All store keys should be cleared
      storesToClear.forEach((key) => {
        expect(localStorage.getItem(key)).toBeNull()
      })

      // user-stats-storage should remain (it's only cleared in clearAuthCaches)
      expect(localStorage.getItem('user-stats-storage')).toBe('stats-data')
    })
  })
})
